package com.zsmall.common.domain.tiktok.domain.dto.order;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Table;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/4 14:41
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@TableName(value = "line_order_compensation")
public class TikTokLineOrderCompensation {
    @TableId
    private Long id;

    private String channelOrderNo;

    private String splitOrCombineTag;

    private String shippingType;

    private String channelType;

    private String orderStatus;

    private Long orderItemId;

    private String trackingNo;

    private String packageId;
    private String orderId;
    /**
     * 状态 0-未补偿 10-已补偿 11-补偿成功 12-补偿失败  13-未经过分销补偿已经发货
     */
    private Integer status;
    private String shopId;
    private String displayStatus;

    /**
     * 错误提示: json格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONObject errorTips;


}
