package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokPackage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/1 18:55
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
public class TikTokPackageData {
    private List<TikTokPackageMsg> packages;
}
