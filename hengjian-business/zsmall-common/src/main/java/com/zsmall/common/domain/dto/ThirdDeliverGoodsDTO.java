package com.zsmall.common.domain.dto;

import com.hengjian.common.core.validate.TripartiteEntryGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/10 16:02
 */
@Data
public class ThirdDeliverGoodsDTO {

    @NotNull(message = "渠道订单号不能为空", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String orderNo;


    @NotNull(message = "三方标识不能为空,渠道店铺标识", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "渠道店铺标识", required = true)
    private String thirdChannelFlag;


    /**
     * 物流进度(Abnormal-异常,NotScanned-未发货,LabelCreated-标签创建,InTransit-已发货,OutForDelivery-派送中,Delayed-延迟,Delivered-已履约 ,Others)
     */
    private String logisticsProgress;

}
