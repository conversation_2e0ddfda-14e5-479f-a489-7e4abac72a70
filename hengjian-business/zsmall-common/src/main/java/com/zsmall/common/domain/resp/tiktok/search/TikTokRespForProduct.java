package com.zsmall.common.domain.resp.tiktok.search;

import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 17:02
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class TikTokRespForProduct extends TikTokRespBaseEntity {


    private SearchProductResp data;
}
