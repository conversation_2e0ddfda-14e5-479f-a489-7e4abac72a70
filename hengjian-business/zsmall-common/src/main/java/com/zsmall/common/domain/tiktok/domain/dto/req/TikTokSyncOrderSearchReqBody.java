package com.zsmall.common.domain.tiktok.domain.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.req.base.TikTokSyncOrderBaseBody;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 17:09
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
//@SuperBuilder
@Getter
@Setter
public class TikTokSyncOrderSearchReqBody extends TikTokSyncOrderBaseBody {
    @JsonProperty("buyer_user_id")
    @JSONField(name="buyer_user_id")
    private String buyerUserid;
    @JsonProperty("create_time_ge")
    @JSONField(name="create_time_ge")
    private long createTimeGe;
    @JsonProperty("create_time_lt")
    @JSONField(name="create_time_lt")
    private long createTimeLt;
    @JsonProperty("is_buyer_request_cancel")
    @JSONField(name="is_buyer_request_cancel")
    private boolean isBuyerRequestCancel;
    @JsonProperty("order_status")
    @JSONField(name="order_status")
    private String orderStatus;
    @JsonProperty("shipping_type")
    @JSONField(name="shipping_type")
    private String shippingType;
    @JsonProperty("update_time_ge")
    @JSONField(name="update_time_ge")
    private long updateTimeGe;
    @JsonProperty("update_time_lt")
    @JSONField(name="update_time_lt")
    private long updateTimeLt;
}
