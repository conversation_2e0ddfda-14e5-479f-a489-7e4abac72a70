package com.zsmall.common.domain.resp.tiktok.product.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.resp.tiktok.product.extend.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/20 22:44
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
@SuperBuilder
public class PackageDetailsResp {

    @JsonProperty("create_time")
    @JSONField(name = "create_time")
    private long createTime;

    @JsonProperty("delivery_option_id")
    @JSONField(name = "delivery_option_id")
    private String deliveryOptionId;

    @JsonProperty("delivery_option_name")
    @JSONField(name = "delivery_option_name")
    private String deliveryOptionName;

    private TikTokDimension dimension;

    @JsonProperty("handover_method")
    @JSONField(name = "handover_method")
    private String handoverMethod;

    @JsonProperty("has_multi_skus")
    @JSONField(name = "has_multi_skus")
    private boolean hasMultiSkus;

    @JsonProperty("note_tag")
    @JSONField(name = "note_tag")
    private String noteTag;

    @JsonProperty("order_line_item_ids")
    @JSONField(name = "order_line_item_ids")
    private List<String> orderLineItemIds;

    private List<PackageOrder> orders;

    @JsonProperty("package_id")
    @JSONField(name = "package_id")
    private String packageId;

    @JsonProperty("package_status")
    @JSONField(name = "package_status")
    private String packageStatus;

    @JsonProperty("pickup_slot")
    @JSONField(name = "pickup_slot")
    private PickupSlot pickupSlot;

    @JsonProperty("recipient_address")
    @JSONField(name = "recipient_address")
    private RecipientAddress recipientAddress;

    @JsonProperty("sender_address")
    @JSONField(name = "sender_address")
    private SenderAddress senderAddress;

    @JsonProperty("shipping_provider_id")
    @JSONField(name = "shipping_provider_id")
    private String shippingProviderId;

    @JsonProperty("shipping_provider_name")
    @JSONField(name = "shipping_provider_name")
    private String shippingProviderName;

    @JsonProperty("shipping_type")
    @JSONField(name = "shipping_type")
    private String shippingType;

    @JsonProperty("split_and_combine_tag")
    @JSONField(name = "split_and_combine_tag")
    private String splitAndCombineTag;

    @JsonProperty("tracking_number")
    @JSONField(name = "tracking_number")
    private String trackingNumber;

    @JsonProperty("update_time")
    @JSONField(name = "update_time")
    private long updateTime;

    private Weight weight;

    private Integer code;

}
