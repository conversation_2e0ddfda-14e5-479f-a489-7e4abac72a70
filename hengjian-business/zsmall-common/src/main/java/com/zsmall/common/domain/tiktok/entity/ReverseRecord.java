package com.zsmall.common.domain.tiktok.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 15:09
 */
@NoArgsConstructor
@AllArgsConstructor
//@Accessors(fluent = true)
@Builder
@Data
@Setter
@Getter
//@TableName("reverse_record")
@ApiModel(value = "逆向订单数据", description = "逆向订单数据")
public class ReverseRecord {

    private Long id;

    @ApiModelProperty(value = "退款表主键")
    private Long orderRefundId;

    @ApiModelProperty(value = "退款表请求主键")
    private Long orderRequestId;


    @JsonProperty("order_id")
    @JSONField(name="order_id")
    @ApiModelProperty(value = "订单号")
    private String orderId;

    @JsonProperty("reverse_order_id")
    @JSONField(name="reverse_order_id")
    @ApiModelProperty(value = "逆向订单号")
    private String reverseOrderId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    public String description;
    /**
     * 逆向更新时间
     */
    @ApiModelProperty(value = "逆向更新时间")
    @JsonProperty("update_time")
    public int updateTime;
    /**
     * 逆向原因文本
     */
    @ApiModelProperty(value = "取消原因")
    @JsonProperty("reason_text")
    public String reasonText;
    /**
     * 附加个人原因
     */
    @ApiModelProperty(value = "买家备注")
    @JsonProperty("additional_message")
    public String additionalMessage;
    /**
     * 附加的图像列表
     */
    @ApiModelProperty(value = "附加的图像列表")
    @JsonProperty("additional_image_list")
    public String additionalImageList;


}
