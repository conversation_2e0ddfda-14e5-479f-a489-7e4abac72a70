package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 16:39
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class Certification implements Serializable {
    private List<TikTokFile> files;
    private String id;
    private List<ImageElement> images;
    private String title;
}
