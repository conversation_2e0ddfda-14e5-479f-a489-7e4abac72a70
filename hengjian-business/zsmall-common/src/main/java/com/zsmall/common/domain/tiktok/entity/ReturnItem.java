package com.zsmall.common.domain.tiktok.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/10 11:20
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Getter
@Setter
@ApiModel(value = "逆向订单数据", description = "逆向订单数据")
public class ReturnItem {
    @JSONField(name="return_product_id")
    @JsonProperty("return_product_id")
    public String returnProductId;

    @JSONField(name="return_product_name")
    @JsonProperty("return_product_name")
    public String returnProductName;

    @JSONField(name="sku_id")
    @JsonProperty("sku_id")
    public String skuId;

    @JSONField(name="seller_sku")
    @JsonProperty("seller_sku")
    public String sellerSku;

    @JSONField(name="sku_name")
    @JsonProperty("sku_name")
    public String skuName;

    @JSONField(name="return_quantity")
    @JsonProperty("return_quantity")
    public int returnQuantity;

    @JSONField(name="product_images")
    @JsonProperty("product_images")
    public String productImages;



}

