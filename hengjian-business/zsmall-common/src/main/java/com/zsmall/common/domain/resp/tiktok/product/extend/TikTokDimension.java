package com.zsmall.common.domain.resp.tiktok.product.extend;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/20 22:45
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
public class TikTokDimension implements Serializable {
    private String height;
    private String length;
    private String unit;
    private String width;
}
