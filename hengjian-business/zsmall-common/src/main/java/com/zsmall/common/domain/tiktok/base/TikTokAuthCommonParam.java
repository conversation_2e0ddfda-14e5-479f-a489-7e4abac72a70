package com.zsmall.common.domain.tiktok.base;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 09:48
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@SuperBuilder
@Accessors(chain = true)
@ApiModel(value = "tiktokCommon", description = "tiktok基础公用参数")
public class TikTokAuthCommonParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 网址
     */
    private String url;
    /**
     * 路径
     */
    private String path;


}
