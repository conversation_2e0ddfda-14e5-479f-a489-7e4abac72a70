package com.zsmall.common.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 物流管理子模块信息
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "物流管理子模块信息")
public class LogisticsConditionBody {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "国家")
    private String country;

    @Schema(title = "物流公司")
    private String logisticsCompany;

    @Schema(title = "有效状态")
    private String statusType;

    @Schema(title = "重量单位")
    private String weightUnit;

    @Schema(title = "长度单位")
    private String lengthUnit;

    @Schema(title = "子模板条件信息")
    private List<LogisticsConditionInfoBody> itemInfoList;

    @Schema(title = "仓库code")
    private String storeWarehouseCode;

    @Schema(title = "仓库id")
    private Long storeWarehouseId;

    @Schema(title = "条件名称")
    private String fulfillerName;

    @Schema(title = "物流账号")
    private String logisticsCompanyId;

    @Schema(title = "仓库信息")
    private StoreWarehouseBody warehouse;
}
