package com.zsmall.common.domain.tiktok.domain.dto.packages;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/2 18:23
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokCreatePackageData {
    @JsonProperty("create_time")
    @JSONField(name="create_time")
    private long createTime;
    private TikTokDimension dimension;
    @JsonProperty("order_id")
    @JSONField(name="order_id")
    private String orderId;
    @JsonProperty("order_line_item_ids")
    @JSONField(name="order_line_item_ids")
    private List<String> orderLineItemIds;
    @JsonProperty("package_id")
    @JSONField(name="package_id")
    private String packageId;
    @JsonProperty("shipping_service_info")
    @JSONField(name="shipping_service_info")
    private TikTokShippingServiceInfo shippingServiceInfo;
    private TikTokWeight weight;
}
