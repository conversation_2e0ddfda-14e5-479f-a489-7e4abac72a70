package com.zsmall.common.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * lty notes 原始订单接收
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 15:54
 */
@Data
public class SaleOrderDetailDTO {
    // @NotNull(message = "",groups = {TripartiteEntryGroup.class})
    // @ApiModelProperty(value = "",required = true)

    /**
     * 仓库编码自提必填
     */
    private String warehouseCode;
    /**
     * 系统承运商
     */
     private String carrier;
    /**
     * 物流单号
     */
    private String logisticsTrackingNo;

    private List<AttachInfo> attachInfoItems;

    /**
     * 是否发货 0不发货 1发货
     */
    private Integer isNeedDelivery;

    private String shipServiceLevel;
}
