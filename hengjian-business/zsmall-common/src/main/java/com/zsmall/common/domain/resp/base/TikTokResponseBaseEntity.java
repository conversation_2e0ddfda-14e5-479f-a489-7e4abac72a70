package com.zsmall.common.domain.resp.base;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 13:39
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Data
@ApiModel(value = "抖音返回数据基础参数", description = "抖音返回数据基础参数")
public class TikTokResponseBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "接口访问状态 0 1")
    public Integer code;

    @ApiModelProperty(value = "success 或者失败信息")
    public String message;

    @JsonProperty("request_id")
    @JSONField(name = "request_id")
    @ApiModelProperty(value = "Request log")
    public String requestId;

    @JsonProperty("data")
    @JSONField(name = "data")
    @Schema(description = "data")
    public Object data;
}
