package com.zsmall.common.domain.tiktok.domain.dto.price;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 16:29
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokItemTax {

    @JsonProperty("tax_amount")
    @JSONField(name="tax_amount")
    private String taxAmount;
    @JsonProperty("tax_rate")
    @JSONField(name="tax_rate")
    private String taxRate;
    @JsonProperty("tax_type")
    @JSONField(name="tax_type")
    private String taxType;
}
