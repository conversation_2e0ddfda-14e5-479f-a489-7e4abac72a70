package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/2 10:38
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
public class TikTokSyncOrderSearchResp {
    @JsonProperty("next_page_token")
    @JSONField(name="next_page_token")
    private String nextPageToken;
    private List<TikTokOrder> orders;
    @JsonProperty("total_count")
    @JSONField(name="total_count")
    private long totalCount;
}
