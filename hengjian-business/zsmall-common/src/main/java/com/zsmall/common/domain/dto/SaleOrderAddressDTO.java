package com.zsmall.common.domain.dto;

import com.hengjian.common.core.validate.TripartiteEntryGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 15:54
 */
@Data
public class SaleOrderAddressDTO {



    /**
     * 地址1
     */

    private String addressline1;
    /**
     * 地址2
     */

    private String addressline2;
    /**
     * 地址3
     */

    private String addressline3;
    /**
     * 地址类型
     */

    private String addressType;
    /**
     * 国家
     */

    private String county;
    /**
     * 州或省份
     */

    private String state;


    /**
     * 城市
     */
    @ApiModelProperty(value = "城市", required = false)

    private String city;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编", required = false)

    private String postalCode;
    /**
     * 国家编码
     */

    @NotNull(message = "国家编码不能为空", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "国家编码", required = true)
    private String countryCode;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话", required = false)
    private String phone;



}
