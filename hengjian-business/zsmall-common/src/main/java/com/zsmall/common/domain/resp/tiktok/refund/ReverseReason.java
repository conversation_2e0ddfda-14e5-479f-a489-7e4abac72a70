package com.zsmall.common.domain.resp.tiktok.refund;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 14:58
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class ReverseReason implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("reverse_reason_key")
    @JSONField(name = "reverse_reason_key")
    private String reverseReasonKey;
    @JsonProperty("reverse_reason")
    @JSONField(name = "reverse_reason")
    private String reverseReason;
    @JsonProperty("available_order_status_list")
    @JSONField(name = "available_order_status_list")
    private int[] availableOrderStatusList;
}
