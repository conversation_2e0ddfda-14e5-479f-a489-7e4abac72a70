package com.zsmall.common.domain.tiktok.domain.dto.item;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.price.TikTokItemTax;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 16:27
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokLineItem {
    private String id;
    @JsonProperty("cancel_reason")
    @JSONField(name="cancel_reason")
    private String cancelReason;

    @JsonProperty("cancel_user")
    @JSONField(name="cancel_user")
    private String cancelUser;
    private String currency;

    @JsonProperty("display_status")
    @JSONField(name="display_status")
    private String displayStatus;



    @JsonProperty("is_gift")
    @JSONField(name="is_gift")
    private Boolean isGift;

    @JsonProperty("item_tax")
    @JSONField(name="item_tax")
    private List<TikTokItemTax> itemTax;

    @JsonProperty("original_price")
    @JSONField(name="original_price")
    private String originalPrice;

    @JsonProperty("package_id")
    @JSONField(name="package_id")
    private String packageId;

    @JsonProperty("package_status")
    @JSONField(name="package_status")
    private String packageStatus;

    @JsonProperty("platform_discount")
    @JSONField(name="platform_discount")
    private String platformDiscount;

    @JsonProperty("product_id")
    @JSONField(name="product_id")
    private String productId;

    @JsonProperty("product_name")
    @JSONField(name="product_name")
    private String productName;

    @JsonProperty("retail_delivery_fee")
    @JSONField(name="retail_delivery_fee")
    private String retailDeliveryFee;

    @JsonProperty("rts_time")
    @JSONField(name="rts_time")
    private Long rtsTime;

    @JsonProperty("sale_price")
    @JSONField(name="sale_price")
    private String salePrice;

    @JsonProperty("seller_discount")
    @JSONField(name="seller_discount")
    private String sellerDiscount;

    @JsonProperty("seller_sku")
    @JSONField(name="seller_sku")
    private String sellerSku;

    @JsonProperty("shipping_provider_id")
    @JSONField(name="shipping_provider_id")
    private String shippingProviderId;

    @JsonProperty("shipping_provider_name")
    @JSONField(name="shipping_provider_name")
    private String shippingProviderName;

    @JsonProperty("sku_id")
    @JSONField(name="sku_id")
    private String skuId;

    @JsonProperty("sku_image")
    @JSONField(name="sku_image")
    private String skuImage;

    @JsonProperty("sku_name")
    @JSONField(name="sku_name")
    private String skuName;

    @JsonProperty("sku_type")
    @JSONField(name="sku_type")
    private String skuType;

    @JsonProperty("small_order_fee")
    @JSONField(name="small_order_fee")
    private String smallOrderFee;

    @JsonProperty("tracking_number")
    @JSONField(name="tracking_number")
    private String trackingNumber;
}
