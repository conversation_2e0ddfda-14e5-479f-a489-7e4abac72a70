package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import com.zsmall.common.domain.tiktok.domain.dto.packages.TikTokCreatePackageData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/2 18:22
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
public class TikTokCreatePackageResp extends TikTokRespBaseEntity {
    private TikTokCreatePackageData data;

}
