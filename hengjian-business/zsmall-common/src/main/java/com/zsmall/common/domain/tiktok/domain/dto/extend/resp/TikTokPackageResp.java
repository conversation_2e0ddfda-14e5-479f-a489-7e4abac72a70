package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/1 18:54
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
public class TikTokPackageResp extends TikTokRespBaseEntity {
    private TikTokPackageData data;
}
