package com.zsmall.common.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * 亚马逊OpenSearch定时任务VO
 *
 * @TableName task_open_search
 */
@Data
public class TaskOpenSearchVo {

    private Long id;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 任务参数正文（根据任务类型的不同，可能有不同的作用）
     */
    private String taskContent;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;


}
