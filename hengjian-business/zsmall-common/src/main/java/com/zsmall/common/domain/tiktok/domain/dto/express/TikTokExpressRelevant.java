package com.zsmall.common.domain.tiktok.domain.dto.express;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/8 13:41
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
public class TikTokExpressRelevant {

    private String trackingNumber;
    private String shipMethod;
    private String fileUrl;
}
