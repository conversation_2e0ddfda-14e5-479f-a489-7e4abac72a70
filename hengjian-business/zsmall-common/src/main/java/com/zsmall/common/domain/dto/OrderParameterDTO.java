package com.zsmall.common.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 订单参数DTO
 * 用于自定义方法传参
 *
 * <AUTHOR>
 * @create 2021/8/24 16:33
 */
@Data
@Accessors(chain = true)
public class OrderParameterDTO {

    /**
     * 是否拥有物流信息（用于Others导入订单在计算价格时计算是否需要付运费）
     */
    private boolean hasTracking;

    /**
     * 国家，计算运费使用，除了US之外的国家，不需要计算运费
     */
    private String country;

    /**
     * 邮编，计算运费使用
     */
    private String zipCode;

    /**
     * 指定仓库
     */
    private String specifyWarehouse;

    /**
     * 指定仓库集合
     */
    private List<String> specifyWarehouseList;

    /**
     * 第三方发货商支持
     */
    private Boolean thirdCarrierSupport;

    /**
     * 活动编号
     */
    private String activityCode;

    public OrderParameterDTO() {
    }

    public OrderParameterDTO(String zipCode, String activityCode) {
        this.zipCode = zipCode;
        this.activityCode = activityCode;
    }

    public OrderParameterDTO(boolean hasTracking, String zipCode) {
        this.hasTracking = hasTracking;
        this.zipCode = zipCode;
    }

    public OrderParameterDTO(boolean hasTracking, String zipCode, String specifyWarehouse) {
        this.hasTracking = hasTracking;
        this.zipCode = zipCode;
        this.specifyWarehouse = specifyWarehouse;
    }

    public OrderParameterDTO(boolean hasTracking, String zipCode, String specifyWarehouse, Boolean thirdCarrierSupport) {
        this.hasTracking = hasTracking;
        this.zipCode = zipCode;
        this.specifyWarehouse = specifyWarehouse;
        this.thirdCarrierSupport = thirdCarrierSupport;
    }

    public OrderParameterDTO(boolean hasTracking, String country, String zipCode, String specifyWarehouse, Boolean thirdCarrierSupport) {
        this.country = country;
        this.hasTracking = hasTracking;
        this.zipCode = zipCode;
        this.specifyWarehouse = specifyWarehouse;
        this.thirdCarrierSupport = thirdCarrierSupport;
    }

    public OrderParameterDTO(boolean hasTracking, String country, String zipCode, String specifyWarehouse, Boolean thirdCarrierSupport, String activityCode) {
        this.country = country;
        this.hasTracking = hasTracking;
        this.zipCode = zipCode;
        this.specifyWarehouse = specifyWarehouse;
        this.thirdCarrierSupport = thirdCarrierSupport;
        this.activityCode = activityCode;
    }

    public static OrderParameterDTO newDTO(boolean hasTracking, String zipCode) {
        return new OrderParameterDTO(hasTracking, zipCode);
    }

    public static OrderParameterDTO newDTO(boolean hasTracking, String zipCode, String specifyWarehouse) {
        return new OrderParameterDTO(hasTracking, zipCode, specifyWarehouse);
    }

    public static OrderParameterDTO newDTO(boolean hasTracking, String zipCode, String specifyWarehouse, Boolean thirdCarrierSupport) {
        return new OrderParameterDTO(hasTracking, zipCode, specifyWarehouse, thirdCarrierSupport);
    }

    public static OrderParameterDTO newDTO(boolean hasTracking, String country, String zipCode, String specifyWarehouse, Boolean thirdCarrierSupport) {
        return new OrderParameterDTO(hasTracking, country, zipCode, specifyWarehouse, thirdCarrierSupport);
    }

    public static OrderParameterDTO newDTO(boolean hasTracking, String country, String zipCode, String specifyWarehouse, Boolean thirdCarrierSupport, String activityCode) {
        return new OrderParameterDTO(hasTracking, country, zipCode, specifyWarehouse, thirdCarrierSupport, activityCode);
    }

    public static OrderParameterDTO newDTO(String zipCode, String activityCode) {
        return new OrderParameterDTO(zipCode, activityCode);
    }

}
