package com.zsmall.common.domain.tiktok.domain.dto.order;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokLineItem;
import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokPackage;
import com.zsmall.common.domain.tiktok.domain.dto.payment.TikTokPayment;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import java.util.List;

/**
 * 功能描述： 订单详情返回
 *
 * <AUTHOR>
 * @date 2024/04/03
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokOrder {

    //    @TableId
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
    @JsonProperty("line_items")
    @JSONField(name = "line_items")
    private List<TikTokLineItem> lineItems;
    @JsonProperty("id")
    @JSONField(name = "id")
    private String tikTokOrderId;

    @JsonProperty("buyer_email")
    @JSONField(name = "buyer_email")
    private String buyerEmail;

    @JsonProperty("buyerMessage")
    @JSONField(name = "buyerMessage")
    private String buyerMessage;

    @JsonProperty("cancel_order_sla_time")
    @JSONField(name = "cancel_order_sla_time")
    private Long cancelOrderSlaTime;

    @JsonProperty("cancel_reason")
    @JSONField(name = "cancel_reason")
    private String cancelReason;

    @JsonProperty("cancel_time")
    @JSONField(name = "cancel_time")
    private Long cancelTime;

    @JsonProperty("cancellation_initiator")
    @JSONField(name = "cancellation_initiator")
    private String cancellationInitiator;

    @JsonProperty("collection_due_time")
    @JSONField(name = "collection_due_time")
    private Long collectionDueTime;

    @JsonProperty("collection_time")
    @JSONField(name = "collection_time")
    private Long collectionTime;

    private String cpf;

    @JsonProperty("create_time")
    @JSONField(name = "create_time")
    private Long createTime;

    @JsonProperty("delivery_due_time")
    @JSONField(name = "delivery_due_time")
    private Long deliveryDueTime;

    @JsonProperty("delivery_option_id")
    @JSONField(name = "delivery_option_id")
    private String deliveryOptionId;

    @JsonProperty("delivery_option_name")
    @JSONField(name = "delivery_option_name")
    private String deliveryOptionName;

    @JsonProperty("delivery_option_required_delivery_time")
    @JSONField(name = "delivery_option_required_delivery_time")
    private Long deliveryOptionRequiredDeliveryTime;

    @JsonProperty("delivery_sla_time")
    @JSONField(name = "delivery_sla_time")
    private Long deliverySlaTime;

    @JsonProperty("delivery_time")
    @JSONField(name = "delivery_time")
    private Long deliveryTime;

    @JsonProperty("fulfillment_type")
    @JSONField(name = "fulfillment_type")
    private String fulfillmentType;

    @JsonProperty("has_updated_recipient_address")
    @JSONField(name = "has_updated_recipient_address")
    private Boolean hasUpdatedRecipientAddress;

    @JsonProperty("is_buyer_request_cancel")
    @JSONField(name = "is_buyer_request_cancel")
    private Boolean isBuyerRequestCancel;

    @JsonProperty("is_cod")
    @JSONField(name = "is_cod")
    private Boolean isCod;

    @JsonProperty("is_on_hold_order")
    @JSONField(name = "is_on_hold_order")
    private Boolean isOnHoldOrder;

    @JsonProperty("is_sample_order")
    @JSONField(name = "is_sample_order")
    private String isSampleOrder;


    @JsonProperty("need_upload_invoice")
    @JSONField(name = "need_upload_invoice")
    private String needUploadInvoice;

    private List<TikTokPackage> packages;

    @JsonProperty("paid_time")
    @JSONField(name = "paid_time")
    private Long paidTime;

    private TikTokPayment payment;

    @JsonProperty("payment_method_name")
    @JSONField(name = "payment_method_name")
    private String paymentMethodName;

    @JsonProperty("recipient_address")
    @JSONField(name = "recipient_address")
    private TikTokRecipientAddress recipientAddress;

    @JsonProperty("request_cancel_time")
    @JSONField(name = "request_cancel_time")
    private Long requestCancelTime;

    @JsonProperty("rts_sla_time")
    @JSONField(name = "rts_sla_time")
    private Long rtsSlaTime;

    @JsonProperty("rts_time")
    @JSONField(name = "rts_time")
    private Long rtsTime;

    @JsonProperty("seller_note")
    @JSONField(name = "seller_note")
    private String sellerNote;

    @JsonProperty("shipping_due_time")
    @JSONField(name = "shipping_due_time")
    private Long shippingDueTime;

    @JsonProperty("shipping_provider")
    @JSONField(name = "shipping_provider")
    private String shippingProvider;

    @JsonProperty("shipping_provider_id")
    @JSONField(name = "shipping_provider_id")
    private String shippingProviderId;

    @JsonProperty("shipping_type")
    @JSONField(name = "shipping_type")
    private String shippingType;

    @JsonProperty("split_or_combine_tag")
    @JSONField(name = "split_or_combine_tag")
    private String splitOrCombineTag;

    /**
     * 订单状态
     *
     * @SEE OrderStatusEnum
     */
    private String status;

    @JsonProperty("tracking_number")
    @JSONField(name = "tracking_number")
    private String trackingNumber;

    @JsonProperty("tts_sla_time")
    @JSONField(name = "tts_sla_time")
    private Long ttsSlaTime;

    @JsonProperty("update_time")
    @JSONField(name = "update_time")
    private Long updateTime;

    @JsonProperty("user_id")
    @JSONField(name = "user_id")
    private String userId;

    @JsonProperty("warehouse_id")
    @JSONField(name = "warehouse_id")
    private String warehouseId;


    private String lineItemId;
    private String packageId;

}
