package com.zsmall.common.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 物流管理子模块详细信息
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "物流管理子模块详细信息")
public class LogisticsConditionInfoBody {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "最小区域")
    private Integer minZone;

    @Schema(title = "最大区域")
    private Integer maxZone;

    @Schema(title = "最小重量")
    private Double minWeight;

    @Schema(title = "最大重量")
    private Double maxWeight;

    @Schema(title = "长")
    private Double length;

    @Schema(title = "宽")
    private Double width;

    @Schema(title = "高")
    private Double height;

    @Schema(title = "运费价格")
    private Double shippingCost;

    @Schema(title = "运费价格")
    private String cost;

    @Schema(title = "区域")
    private String zone;

    @Schema(title = "重量")
    private String weight;

    @Schema(title = "长宽高")
    private String lWeightHeight;
}
