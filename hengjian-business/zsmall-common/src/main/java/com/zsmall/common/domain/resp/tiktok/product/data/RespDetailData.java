package com.zsmall.common.domain.resp.tiktok.product.data;

import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import com.zsmall.common.domain.resp.tiktok.product.resp.ProductDetailsResp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/20 13:46
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
@SuperBuilder
public class RespDetailData extends TikTokResponseBaseEntity {

    private ProductDetailsResp data;

}
