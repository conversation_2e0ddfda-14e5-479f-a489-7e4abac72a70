package com.zsmall.common.domain.base;

import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> <PERSON>
 * @create 2024/3/29 10:21
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@SuperBuilder
@Accessors(chain = true)

public class ApiReturnMsg {
    private String channel;
    private String orderNo;
    private String msg;
    private String errorMsg;
}
