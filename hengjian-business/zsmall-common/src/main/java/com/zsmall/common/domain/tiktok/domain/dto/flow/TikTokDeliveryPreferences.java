package com.zsmall.common.domain.tiktok.domain.dto.flow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 16:36
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokDeliveryPreferences {
    @JsonProperty("drop_off_location")
    @JSONField(name = "drop_off_location")
    private String dropOffLocation;
}
