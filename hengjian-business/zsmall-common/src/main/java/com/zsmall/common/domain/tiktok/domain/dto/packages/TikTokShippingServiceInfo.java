package com.zsmall.common.domain.tiktok.domain.dto.packages;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/2 18:25
 */
public class TikTokShippingServiceInfo {
    private String currency;
    @JsonProperty("earliest_delivery_days")
    @JSONField(name="earliest_delivery_days")
    private long earliestDeliveryDays;
    private String id;
    @JsonProperty("latest_delivery_days")
    @JSONField(name="latest_delivery_days")
    private long latestDeliveryDays;
    private String name;
    private String price;
    @JsonProperty("shipping_provider_id")
    @JSONField(name="shipping_provider_id")
    private String shippingProviderId;
    @JsonProperty("shipping_provider_name")
    @JSONField(name="shipping_provider_name")
    private String shippingProviderName;
}
