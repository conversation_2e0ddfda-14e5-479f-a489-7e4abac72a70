package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hengjian.common.core.validate.TripartiteEntryGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/19 10:27
 */
@Data
public class OpenApiSaleOrderItemDTO {
    private String skuId;

    /**
     * erpSku,例: ZJHJ:xxx-xxx
     */
    @NotNull(message = "erpSku不能为空",groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "erpSku,例: ZJHJ:xxx-xxx",required = true)
    @JSONField(name = "erp_sku")
    @JsonProperty("erp_sku")
    private String erpSku;


    /**
     * 数量
     */
    @NotNull(message = "数量不可为空",groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "数量",required = true)
    @JSONField(name = "quantity")
    @JsonProperty("quantity")
    private Integer quantity;

    /**
     * 单价
     */
    @NotNull(message = "商品单价不可为空",groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "商品单价",required = true)
    @JSONField(name = "unit_price")
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;

}
