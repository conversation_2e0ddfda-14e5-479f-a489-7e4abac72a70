package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/1 18:57
 */
@Data
public class TikTokPackageMsg {
    private String id;
    @JsonProperty("splittable_group_id")
    @JSONField(name="splittable_group_id")
    private String splittableGroupId;
}
