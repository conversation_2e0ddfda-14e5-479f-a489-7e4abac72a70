package com.zsmall.common.domain.resp.tiktok.product.data;

import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import com.zsmall.common.domain.resp.tiktok.product.resp.PackageDetailsResp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/21 11:09
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
@SuperBuilder
public class PackageDetailsData extends TikTokResponseBaseEntity {

    private PackageDetailsResp data;
}
