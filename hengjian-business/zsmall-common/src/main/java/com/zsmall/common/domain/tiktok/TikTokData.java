package com.zsmall.common.domain.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/9 18:33
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
@ApiModel(value = "抖音data数据", description = "抖音data数据")
public class TikTokData implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("order_id")
    @ApiModelProperty(value = "订单id", required = true)
    private String orderId;

    @JsonProperty("reverse_type")
    @ApiModelProperty(value = "逆向订单通知类型", required = true)
    private Integer reverseType;

    @JsonProperty("reverse_user")
    @ApiModelProperty(value = "逆向用户", required = true)
    private String reverseUser;

    @JsonProperty("reverse_order_status")
    @ApiModelProperty(value = "逆向订单状态", required = true)
    private String reverseOrderStatus;

    @JsonProperty("reverse_event_type")
    @ApiModelProperty(value = "事件类型", required = true)
    private String reverseEventType;

    @JsonProperty("reverse_order_id")
    @ApiModelProperty(value = "逆向订单id", required = true)
    private String reverseOrderId;

    @JsonProperty("update_time")
    @ApiModelProperty(value = "更新时间", required = true)
    private Long updateTime;

}
