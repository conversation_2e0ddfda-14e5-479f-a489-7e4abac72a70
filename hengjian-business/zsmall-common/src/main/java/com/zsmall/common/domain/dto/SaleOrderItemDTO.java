package com.zsmall.common.domain.dto;

import com.hengjian.common.core.validate.TripartiteEntryGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * lty notes 原始订单 产品项接收
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 15:54
 */
@Data
@Accessors(chain = true)
public class SaleOrderItemDTO {


    private String skuId;

    /**
     * erpSku,例: ZJHJ:xxx-xxx
     */
    @NotNull(message = "erpSku不能为空",groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "erpSku,例: ZJHJ:xxx-xxx",required = true)
    private String erpSku;

    /**
     * 型号:内部使用
     */
    private String itemNo;
    /**
     * 数量
     */
    @NotNull(message = "数量不可为空",groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "数量",required = true)
    private Integer quantity;

    /**
     * 单价
     */
    @NotNull(message = "商品单价不可为空",groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "商品单价",required = true)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "erpSku,例: ZJHJ:xxx-xxx,包含前缀",required = true)
    private String havePrefixErpSku;

//    private SaleOrderAddressDTO saleOrderAddressDTO;



}
