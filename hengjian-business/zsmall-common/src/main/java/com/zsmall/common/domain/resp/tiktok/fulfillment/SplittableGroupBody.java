package com.zsmall.common.domain.resp.tiktok.fulfillment;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.req.base.TikTokSyncOrderBaseBody;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/10 14:50
 */
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class SplittableGroupBody extends TikTokSyncOrderBaseBody {
    @JsonProperty("splittable_groups")
    @JSONField(name="splittable_groups")
    private List<SplittableGroup> splittableGroups;

    public SplittableGroupBody(List<SplittableGroup> splittableGroups) {
        this.splittableGroups = splittableGroups;
    }
}
