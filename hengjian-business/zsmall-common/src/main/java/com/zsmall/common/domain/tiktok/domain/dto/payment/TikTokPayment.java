package com.zsmall.common.domain.tiktok.domain.dto.payment;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 16:27
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokPayment {
    private String currency;

    @JsonProperty("original_shipping_fee")
    @JSONField(name="original_shipping_fee")
    private String originalShippingFee;

    @JsonProperty("original_total_product_price")
    @JSONField(name="original_total_product_price")
    private String originalTotalProductPrice;

    @JsonProperty("platform_discount")
    @JSONField(name="platform_discount")
    private String platformDiscount;

    @JsonProperty("product_tax")
    @JSONField(name="product_tax")
    private String productTax;

    @JsonProperty("retail_delivery_fee")
    @JSONField(name="retail_delivery_fee")
    private String retailDeliveryFee;

    @JsonProperty("seller_discount")
    @JSONField(name="seller_discount")
    private String sellerDiscount;

    @JsonProperty("shipping_fee")
    @JSONField(name="shipping_fee")
    private String shippingFee;

    @JsonProperty("shipping_fee_seller_discount")
    @JSONField(name="shipping_fee_seller_discount")
    private String shippingFeePlatformDiscount;

    @JsonProperty("shipping_fee_platform_discount")
    @JSONField(name="shipping_fee_platform_discount")
    private String shippingFeeSellerDiscount;

    @JsonProperty("shipping_fee_tax")
    @JSONField(name="shipping_fee_tax")
    private String shippingFeeTax;

    @JsonProperty("small_order_fee")
    @JSONField(name="small_order_fee")
    private String smallOrderFee;

    @JsonProperty("sub_total")
    @JSONField(name="sub_total")
    private String subTotal;

    private String tax;

    @JsonProperty("total_amount")
    @JSONField(name="total_amount")
    private String totalAmount;
}
