package com.zsmall.common.domain.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.req.base.TikTokRequestBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 14:54
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class RejectReasonListReq extends TikTokRequestBaseEntity {
    /**
     * 反转动作类型
     * CANCEL = 1;
     * REFUND = 2;
     * RETURN_AND_REFUND = 3;
     * REQUEST_CANCEL_REFUND = 4;
     */
    @JsonProperty("reverse_action_type")
    @JSONField(name="reverse_action_type")
    private Integer reverseActionType;
    /**
     * 原因类型
     * Available value:
     * STARTE_REVERSE = 1;
     * REJECT_APPLY = 2;
     * REJECT_PARCEL = 3;
     */
    @JsonProperty("reason_type")
    @JSONField(name="reason_type")
    private Integer reasonType ;
    /**
     * 发货状态
     * Available value:
     * UNINITIATE = 1;
     * BEFORE_RTS = 2;
     * RTS = 3;
     * DELIVERED = 4;
     */
    @JsonProperty("fulfillment_status")
    @JSONField(name="fulfillment_status")
    private Integer fulfillmentStatus;

}

