package com.zsmall.common.domain.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/6/11 14:54
 */
@Data
public class OpenApiOrderCancelResp {
    @JsonProperty("order_no")
    @JSONField(name = "order_no")
    private String orderNo;
    @JsonProperty("channel_order_no")
    @JSONField(name = "channel_order_no")
    private String channelOrderNo;



    @JsonProperty("cancel_status")
    @JSONField(name = "cancel_status")
    private Integer cancelStatus;


    @JsonProperty("cancel_reason")
    @JSONField(name = "cancel_reason")
    private String cancelReason;
}
