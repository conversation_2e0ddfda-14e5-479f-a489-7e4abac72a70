package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.flow.TikTokDeliveryPreferences;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/20 22:49
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
public class RecipientAddress {
    @JsonProperty("address_detail")
    @JSONField(name="address_detail")
    private String addressDetail;
    @JsonProperty("address_line1")
    @JSONField(name="address_line1")
    private String addressLine1;
    @JsonProperty("address_line2")
    @JSONField(name="address_line2")
    private String addressLine2;
    @JsonProperty("address_line3")
    @JSONField(name="address_line3")
    private String addressLine3;
    @JsonProperty("address_line4")
    @JSONField(name="address_line4")
    private String addressLine4;
    @JsonProperty("full_address")
    @JSONField(name="full_address")
    private String fullAddress;
    private String name;
    @JsonProperty("phone_number")
    @JSONField(name="phone_number")
    private String phoneNumber;
    @JsonProperty("postal_code")
    @JSONField(name="postal_code")
    private String postalCode;
    @JsonProperty("region_code")
    @JSONField(name="region_code")
    private String regionCode;
    @JsonProperty("delivery_preferences")
    @JSONField(name="delivery_preferences")
    private TikTokDeliveryPreferences deliveryPreferences;
    @JsonProperty("district_info")
    @JSONField(name="district_info")
    private List<TikTokDistrictInfo> districtInfo;
}
