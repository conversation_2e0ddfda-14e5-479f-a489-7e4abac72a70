package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/19 10:23
 */
@Data
public class OpenSaleOrderDetailDTo {
    /**
     * 仓库编码自提必填
     */
    @JsonProperty("warehouse_code")
    @JSONField(name = "warehouse_code")
    private String warehouseCode;
    /**
     * 承运商
     */
    @JsonProperty("carrier")
    @JSONField(name = "carrier")
    private String carrier;
    /**
     * 物流单号
     */
    @JsonProperty("logistics_tracking_no")
    @JSONField(name = "logistics_tracking_no")
    private String logisticsTrackingNo;

    @JsonProperty("attach_info_items")
    @JSONField(name = "attach_info_items")
    private List<AttachInfo> attachInfoItems;

    /**
     * 是否发货 0不发货 1发货
     */
    @JsonProperty("is_need_delivery")
    @JSONField(name = "is_need_delivery")
    private Integer isNeedDelivery;

    @JsonProperty("ship_service_level")
    @JSONField(name = "ship_service_level")
    private String shipServiceLevel;

}
