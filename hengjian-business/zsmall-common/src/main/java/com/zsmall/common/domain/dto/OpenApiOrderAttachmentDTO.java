package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/1/9 13:44
 */
@Data
public class OpenApiOrderAttachmentDTO {

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    @JSONField(name="order_no")
    private String orderNo;

    /**
     * 附件
     */
    private List<OpenApiAttachmentDTO> attachments;

}
