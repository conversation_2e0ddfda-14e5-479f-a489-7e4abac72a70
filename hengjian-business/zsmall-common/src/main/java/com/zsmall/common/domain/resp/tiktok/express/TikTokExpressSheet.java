package com.zsmall.common.domain.resp.tiktok.express;

import com.zsmall.common.domain.base.ExpressSheet;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/28 17:38
 */

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class TikTokExpressSheet extends ExpressSheet {
    /**
     * 拆分或组合标记
     */
    private String splitOrCombineTag;

}
