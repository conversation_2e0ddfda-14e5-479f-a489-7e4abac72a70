package com.zsmall.common.domain.tiktok.domain.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.domain.dto.req.base.TikTokSyncOrderBaseReq;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 13:46
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class TikTokSyncOrderSearchReq extends TikTokSyncOrderBaseReq {

    @JsonProperty("page_size")
    @JSO<PERSON>ield(name="page_size")
    private int pageSize;
    @JsonProperty("sort_field")
    @JSONField(name="sort_field")
    private String sortField;
    @JsonProperty("sort_order")
    @J<PERSON><PERSON>ield(name="sort_order")
    private String sortOrder;
    @JsonProperty("page_token")
    @JSONField(name="page_token")
    private String pageToken;

}
