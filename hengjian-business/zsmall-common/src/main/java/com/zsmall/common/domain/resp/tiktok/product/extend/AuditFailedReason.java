package com.zsmall.common.domain.resp.tiktok.product.extend;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 16:26
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class AuditFailedReason implements Serializable {

    private String position;

    private List<String> reasons;

    private List<String> suggestions;

}
