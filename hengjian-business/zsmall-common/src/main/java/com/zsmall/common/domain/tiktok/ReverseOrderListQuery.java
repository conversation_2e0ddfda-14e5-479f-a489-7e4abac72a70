package com.zsmall.common.domain.tiktok;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.base.TikTokAuthCommonParam;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 09:51
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
@ApiModel(value = "tiktokCommon", description = "tiktok基础公用参数")
public class ReverseOrderListQuery extends TikTokAuthCommonParam {
    @JsonProperty("shop_id")
    @JSONField(name = "shop_id")
    private String shopId ;
}
