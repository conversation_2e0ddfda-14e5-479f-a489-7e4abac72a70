package com.zsmall.common.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 基础信息-仓库信息
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "基础信息-仓库信息")
public class StoreWarehouseBody {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "仓库code")
    private String warehouseId;

    @Schema(title = "也是仓库code")
    private String warehouseCode;
    @Schema(title = "也是仓库code")
    private String warehouseSystemCode;

    @Schema(title = "仓库服务商名称")
    private String warehouseName;

    @Schema(title = "仓库服务商类型")
    private String warehouseType;

    @Schema(title = "密钥")
    private String privateKey;

    @Schema(title = "经度")
    private Double longitude;

    @Schema(title = "纬度")
    private Double latitude;

    @Schema(title = "接入时间")
    private String accessDate;

    @Schema(title = "国家主键")
    private Long countryId;

    @Schema(title = "国家代号")
    private String countryCode;

    @Schema(title = "国家")
    private String country_zh_CN;

    @Schema(title = "国家")
    private String country_en_US;

    @Schema(title = "州/省主键")
    private Long stateId;

    @Schema(title = "州/省")
    private String state_zh_CN;

    @Schema(title = "州/省")
    private String state_en_US;

    @Schema(title = "城市主键")
    private Long cityId;

    @Schema(title = "城市")
    private String city_zh_CN;

    @Schema(title = "城市")
    private String city_en_US;

    @Schema(title = "商品数量")
    private Integer productNum = 0;

    @Schema(title = "仓库完整地址")
    private String address;

    @Schema(title = "仓库地址1")
    private String address1;

    @Schema(title = "仓库地址2")
    private String address2;

    @Schema(title = "zipCode")
    private String zipCode;

    @Schema(title = "仓库管理者")
    private String managerName;

    @Schema(title = "联系电话")
    private String contactNumber;

    @Schema(title = "key1")
    private String key1;

    @Schema(title = "key2")
    private String key2;

    @Schema(title = "key3")
    private String key3;

    @Schema(title = "状态")
    private String status;

    @Schema(title = "编辑模式")
    private Boolean isEdit;

    @Schema(title = "默认状态")
    private Boolean defaultState = false;

    @Schema(title = "城市（手输）")
    private String city;

    @Schema(title = "洲/省（手输）")
    private String stateName;

    @Schema(title = "是否支持第三方物流商")
    private Boolean thirdCarrierSupport = false;

    @Schema(title = "FedEx发货方式名称")
    private String fedExName;

    @Schema(title = "UPS发货方式名称")
    private String upsName;

    @Schema(title = "仓库系统编码")
    private String systemCode;
}
