package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2025/01/09
 */
@Data
public class OpenApiAttachmentDTO {
    /**
     * url
     */
    private String url;

    /**
     *  附件类型:Label:0 BOL:2 CartonLabel:4 PalletLabel:5 ItemLabel:6
     * */
    @JsonProperty("file_type")
    @JSONField(name = "file_type")
    private Integer fileType;

    private String name;

    private String remark;
    @JSONField(name = "tracking_no")
    private String trackingNo;
}
