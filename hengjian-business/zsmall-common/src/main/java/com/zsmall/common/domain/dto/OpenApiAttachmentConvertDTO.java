package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/7 14:27
 */
@Data
public class OpenApiAttachmentConvertDTO {

    /**
     * base64编码
     */
    @JsonProperty("base64_code")
    @JSONField(name = "base64_code")
    private String base64Code;

    /**
     * 文件名
     */
    @JsonProperty("file_name")
    @JSONField(name = "file_name")
    private String fileName;

}
