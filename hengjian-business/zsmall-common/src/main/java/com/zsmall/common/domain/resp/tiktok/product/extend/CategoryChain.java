package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 16:30
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class CategoryChain implements Serializable {

    private String id;

    @JsonProperty("is_leaf")
    @JSONField(name = "is_leaf")
    private Boolean isLeaf;

    @JsonProperty("local_name")
    @J<PERSON>NField(name = "local_name")
    private String localName;

    @JsonProperty("parent_id")
    @JSONField(name = "parent_id")
    private String parentId;
}
