package com.zsmall.common.domain.tiktok.domain.dto.address;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 16:37
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokDistrictInfo {
    @JsonProperty("address_level_name")
    @JSONField(name="address_level_name")
    private String addressLevelName;
    @JsonProperty("address_name")
    @JSONField(name="address_name")
    private String addressName;
    @JsonProperty("address_level")
    @JSONField(name="address_level")
    private String addressLevel;
}
