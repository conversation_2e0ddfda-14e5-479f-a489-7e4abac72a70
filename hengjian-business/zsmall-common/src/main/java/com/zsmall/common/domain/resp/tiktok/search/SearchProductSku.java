package com.zsmall.common.domain.resp.tiktok.search;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 17:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchProductSku {
    private String id;
    private List<SearchProductInventory> inventory;
    private SearchPrice price;
    private String sellerSku;

    /**
     * 主图存放路径
     */
    private String imageSavePath;

    /**
     * 主图展示地址
     */
    private String imageShowUrl;
}
