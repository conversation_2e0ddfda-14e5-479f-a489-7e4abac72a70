package com.zsmall.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/22 19:06
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class CompensationOrderDTO {

    private List<String> orderNo;
    private String shopId;
    private String status;
    private String tenantId;

}
