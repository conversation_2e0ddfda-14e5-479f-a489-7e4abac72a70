package com.zsmall.common.domain.resp.tiktok.order;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.resp.tiktok.product.extend.RecipientAddress;
import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokPackage;
import com.zsmall.common.domain.tiktok.domain.dto.payment.TikTokPayment;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/25 18:01
 */
public class TikTokOrderDetails {
    @JsonProperty("buyer_email")
    @JSONField(name="buyer_email")
    private String buyerEmail;
    @JsonProperty("buyer_message")
    @JSONField(name="buyer_message")
    private String buyerMessage;
    @JsonProperty("cancel_order_sla_time")
    @JSONField(name="cancel_order_sla_time")
    private Long cancelOrderSlaTime;
    @JsonProperty("cancel_reason")
    @JSONField(name="cancel_reason")
    private String cancelReason;
    @JsonProperty("cancel_time")
    @JSONField(name="cancel_time")
    private Long cancelTime;
    @JsonProperty("cancellation_initiator")
    @JSONField(name="cancellation_initiator")
    private String cancellationInitiator;
    @JsonProperty("")
    @JSONField(name="")
    private Long collectionDueTime;
    private Long collectionTime;
    private String cpf;
    private Long createTime;
    private Long deliveryDueTime;
    private String deliveryOptionid;
    private String deliveryOptionName;
    private Long deliveryOptionRequiredDeliveryTime;
    private Long deliverySlaTime;
    private Long deliveryTime;
    private String fulfillmentType;
    private Boolean hasUpdatedRecipientAddress;
    private String id;
    private Boolean isBuyerRequestCancel;
    private Boolean isCod;
    private Boolean isOnHoldOrder;
    private Boolean isReplacementOrder;
    private Boolean isSampleOrder;
    private List<TikTokOrderItemDetails> lineItems;
    private String needUploadInvoice;
    private List<TikTokPackage> packages;
    private Long paidTime;
    private TikTokPayment payment;
    private String paymentMethodName;
    private RecipientAddress recipientAddress;
    private String replacedOrderid;
    private Long requestCancelTime;
    private Long rtsSlaTime;
    private Long rtsTime;
    private String sellerNote;
    private Long shippingDueTime;
    private String shippingProvider;
    private String shippingProviderid;
    private String shippingType;
    private String splitOrCombineTag;
    private String status;
    private String trackingNumber;
    private Long ttsSlaTime;
    private Long updateTime;
    private String userid;
    private String warehouseid;
}
