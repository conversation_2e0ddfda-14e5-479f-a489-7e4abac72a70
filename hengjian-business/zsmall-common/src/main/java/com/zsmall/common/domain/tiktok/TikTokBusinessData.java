package com.zsmall.common.domain.tiktok;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/9 18:07
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
@ApiModel(value = "抖音webhook数据", description = "抖音webhook数据")
public class TikTokBusinessData implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "类型不能为空")
    @ApiModelProperty(value = "类型", required = true)
    private Integer type;

    @NotNull(message = "店铺id不能为空")
    @JsonProperty("shop_id")
    @ApiModelProperty(value = "店铺id", required = true)
    private String shopId;

    @NotNull(message = "时间戳不能为空")
    @ApiModelProperty(value = "时间戳", required = true)
    private Long timestamp;

    @NotNull(message = "tiktok内部数据")
    @ApiModelProperty(value = "时间戳", required = true)
    private TikTokData data;



}
