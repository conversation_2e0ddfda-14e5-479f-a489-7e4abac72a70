package com.zsmall.common.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年6月6日  11:37
 * @description: temu订单 item
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class TemuOrderItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sku;

    private String asin;

    private Integer quantity;

    private BigDecimal subtotal;

    private String currency_code;

    private String channel_order_item_id;

    private BigDecimal price;

    private BigDecimal sales_total_amount;

    private String sku_name;

    private Integer canceled_quantity_before_shipment;

    private String productSkuCode;
    private String ordernum;
}
