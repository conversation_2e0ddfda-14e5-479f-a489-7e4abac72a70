package com.zsmall.common.domain.resp.tiktok.refund;

import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 13:41
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
@SuperBuilder
public class ConfirmReverseResp extends TikTokResponseBaseEntity {

    private Object data;
}
