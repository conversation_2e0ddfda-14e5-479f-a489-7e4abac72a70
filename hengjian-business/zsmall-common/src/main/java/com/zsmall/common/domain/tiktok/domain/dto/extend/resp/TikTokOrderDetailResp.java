package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/3 17:47
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
public class TikTokOrderDetailResp extends TikTokRespBaseEntity {
    private TikTokOrderDetailData data;
}
