package com.zsmall.common.domain.resp.tiktok.order;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/25 18:02
 */
public class TikTokOrderItemDetails {
    private String cancelReason;
    private String cancelUser;
    private List<CombinedListingSkus> combinedListingSkus;
    private String currency;
    private String displayStatus;
    private String id;
    private Boolean isGift;
    private List<TikTokItemTaxDetail> itemTax;
    private String originalPrice;
    private String packageid;
    private String packageStatus;
    private String platformDiscount;
    private String productid;
    private String productName;
    private String retailDeliveryFee;
    private Long rtsTime;
    private String salePrice;
    private String sellerDiscount;
    private String sellerSku;
    private String shippingProviderid;
    private String shippingProviderName;
    private String skuid;
    private String skuImage;
    private String skuName;
    private String skuType;
    private String smallOrderFee;
    private String trackingNumber;
}
