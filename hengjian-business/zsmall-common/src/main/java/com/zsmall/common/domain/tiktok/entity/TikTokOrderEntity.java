package com.zsmall.common.domain.tiktok.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/3 11:42
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@TableName("tik_tok_order")
public class TikTokOrderEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    private String tikTokOrderId;

    private String packageId;

    private String userId;

    private String warehouseId;

    private String trackingNumber;

    /**
     * 分销系统订单号
     */
    private String orderNo;
}
