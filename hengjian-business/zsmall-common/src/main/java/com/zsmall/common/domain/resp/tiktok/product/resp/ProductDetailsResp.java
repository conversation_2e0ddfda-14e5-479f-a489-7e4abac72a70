package com.zsmall.common.domain.resp.tiktok.product.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import com.zsmall.common.domain.resp.tiktok.product.extend.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 16:25
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
@SuperBuilder
public class ProductDetailsResp {

    @JsonProperty("audit_failed_reasons")
    @JSONField(name="audit_failed_reasons")
    private List<AuditFailedReason> auditFailedReasons;

    private Brand brand;
    @JsonProperty("category_chains")
    @JSONField(name="category_chains")
    private List<CategoryChain> categoryChains;

    private List<Certification> certifications;

    private long createTime;
    @JsonProperty("delivery_options")
    @JSONField(name="delivery_options")
    private List<DeliveryOption> deliveryOptions;

    private String description;
    @JsonProperty("external_product_id")
    @JSONField(name="external_product_id")
    private String externalProductId;

    private String id;
    @JsonProperty("is_cod_allowed")
    @JSONField(name="is_cod_allowed")
    private boolean isCodAllowed;

    @JsonProperty("main_images")
    @JSONField(name="main_images")
    private List<MainImage> mainImages;
    @JsonProperty("package_dimensions")
    @JSONField(name="package_dimensions")
    private PackageDimensions packageDimensions;
    @JsonProperty("package_weight")
    @JSONField(name="package_weight")
    private PackageWeight packageWeight;
    @JsonProperty("product_attributes")
    @JSONField(name="product_attributes")
    private List<ProductAttribute> productAttributes;
    @JsonProperty("size_chart")
    @JSONField(name="size_chart")
    private SizeChart sizeChart;

    private List<Skus> skus;

    private String status;

    private String title;
    @JsonProperty("update_time")
    @JSONField(name="update_time")
    private long updateTime;

    private Video video;
}
