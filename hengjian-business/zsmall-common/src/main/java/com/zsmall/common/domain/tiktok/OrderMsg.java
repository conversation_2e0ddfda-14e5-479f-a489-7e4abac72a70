package com.zsmall.common.domain.tiktok;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/10 10:04
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Getter
@Setter
@ApiModel(value = "获取逆向订单接口参数", description = "获取逆向订单接口参数")
public class OrderMsg implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Use this field to specify the offset of the order. Must be greater than or equal to 0.
     * If "offset" is less than 0, then "offset" is assigned the default value of 0.
     */
    @ApiModelProperty(value = "订单偏移量 ")
    private Integer offset;

    /**
     * 反向顺序状态
     * {@link com.bizark.erp.common.enm.TikTokEnum}
     */
    @JsonProperty(value = "reverse_order_status" )
    @JSONField(name = "reverse_order_status")
    private Integer reverseOrderStatus;

    /**
     * 反向类型 JSONField 保证序列化和反序列化都是reverse_type
     */
    @ApiModelProperty(value = "逆向类型 1:取消 2:仅退款 3:退货退款 4:请求取消 ")
    @JsonProperty("reverse_type")
    @JSONField(name = "reverse_type")
    private Integer reverseType;

    @ApiModelProperty(value = "指定此接口可获取的数据量 1~100,小于1按10来处理")
    private Integer size;

    @ApiModelProperty(value = "排序顺序 0:升序 1:降序")
    @JsonProperty("sort_type")
    @JSONField(name = "sort_type")
    private Integer sortType;

    @ApiModelProperty(value = "排序标准 0:请求时间(默认) 1:更新时间 2:退款总额  ")
    @JsonProperty("sort_by")
    @JSONField(name = "sort_by")
    private Integer sortBy;

    @ApiModelProperty(value = "订单id")
    @JsonProperty("order_id")
    @JSONField(name = "order_id")
    private String orderId;

    @ApiModelProperty(value = "逆向订单id")
    @JsonProperty(value = "reverse_order_id")
    @JSONField(name = "reverse_order_id")
    private String reverseOrderId;

    @ApiModelProperty(value = "逆向订单更新时间戳")
    @JsonProperty("update_time_from")
    @JSONField(name = "update_time_from")
    private Long updateTimeFrom;

    @ApiModelProperty(value = "逆向订单更新时间戳")
    @JsonProperty("update_time_to")
    @JSONField(name = "update_time_to")
    private Long updateTimeTo;

}
