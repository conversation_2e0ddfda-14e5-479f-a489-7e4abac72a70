package com.zsmall.common.domain.tiktok.domain.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.resp.tiktok.product.extend.Weight;
import com.zsmall.common.domain.tiktok.domain.dto.packages.TikTokDimension;
import com.zsmall.common.domain.tiktok.domain.dto.req.base.TikTokSyncOrderBaseBody;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/10 09:55
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
public class TikTokCreatePackagesBody extends TikTokSyncOrderBaseBody {

    private TikTokDimension dimension;
    @JsonProperty("order_id")
    @JSONField(name = "order_id")
    private String orderId;
    @JsonProperty("order_line_item_ids")
    @JSONField(name = "order_line_item_ids")
    private List<String> orderLineItemIds;
    @JsonProperty("shipping_service_id")
    @JSONField(name = "shipping_service_id")
    private String shippingServiceId;
    private Weight weight;
}
