package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/6/12 10:31
 */
@Data
public class OpenApiOrderReviewDTO {
    @JsonProperty("tenant_id")
    @JSONField(name = "tenant_id")
    private String tenantId;
    /**
     * 订单审核意见 0:同意 1:拒绝
     */
    @JsonProperty("order_review_opinion")
    @JSONField(name = "order_review_opinion")
    private Integer orderReviewOpinion;
    @JsonProperty("order_no")
    @JSONField(name = "order_no")
    private String orderNo;

}
