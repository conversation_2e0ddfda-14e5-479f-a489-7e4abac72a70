package com.zsmall.common.domain.tiktok.domain.dto.extend.resp;

import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/3 17:48
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class TikTokOrderDetailData {
    private List<TikTokOrder> orders;
}
