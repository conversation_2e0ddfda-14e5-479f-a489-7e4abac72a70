package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 17:06
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class Video implements Serializable {
    @JsonProperty("cover_url")
    @JSONField(name="cover_url")
    private String coverUrl;
    private String format;
    private long height;
    private String id;
    private long size;
    private String url;
    private long width;
}
