package com.zsmall.common.domain.tiktok.domain.dto.req.base;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 17:11
 */
@NoArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Setter
@Getter
public class TikTokSyncOrderBaseReq implements Serializable {
}
