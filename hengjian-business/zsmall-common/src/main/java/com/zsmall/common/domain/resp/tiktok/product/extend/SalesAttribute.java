package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 17:05
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class SalesAttribute implements Serializable {
    private String id;
    private String name;
    @JsonProperty("sku_img")
    @JSONField(name="sku_img")
    private SkuImg skuImg;
    @JsonProperty("value_id")
    @JSONField(name="value_id")
    private String valueId;
    @JsonProperty("value_name")
    @JSONField(name="value_name")
    private String valueName;
}
