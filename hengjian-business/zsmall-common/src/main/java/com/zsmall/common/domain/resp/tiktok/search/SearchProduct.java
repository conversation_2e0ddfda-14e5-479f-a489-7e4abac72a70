package com.zsmall.common.domain.resp.tiktok.search;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 17:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchProduct {
    private Long createTime;
    private String id;
    private String productSyncFailReasons;
    private List<String> salesRegions;
    private List<SearchProductSku> skus;
    private String status;
    private String title;
    private Long updateTime;
}
