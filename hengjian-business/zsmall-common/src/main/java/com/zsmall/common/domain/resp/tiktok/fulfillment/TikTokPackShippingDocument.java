package com.zsmall.common.domain.resp.tiktok.fulfillment;

import com.zsmall.common.domain.tiktok.domain.dto.base.TikTokRespBaseEntity;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/8 14:03
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class TikTokPackShippingDocument extends TikTokRespBaseEntity {

    private PackageShippingDocument data;
}
