package com.zsmall.common.domain.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 15:00
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class RejectReverseReq implements Serializable {
    @JsonProperty("reverse_order_id")
    @JSONField(name="reverse_order_id")
    private String reverseOrderId;

    /**
     * 反向拒绝原因键 reverse_reject_request_reason_1 拼接的最后一个值由 GetReverseReason API的
     */
    @JsonProperty("reverse_reject_reason_key")
    @JSONField(name="reverse_reject_reason_key")
    private String reverseRejectReasonKey;

    @JsonProperty("reverse_reject_comments")
    @JSONField(name="reverse_reject_comments")
    private String reverseRejectComments;
}
