package com.zsmall.common.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年11月6日  16:09
 * @description:易仓订单消息体item
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
public class EcOrderItemMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sku;

//    private String asin;

    private String title;

    private Integer quantity;

    private BigDecimal subtotal;

    private String currency_code;

//    private Integer shiped_quantity;

//    private BigDecimal shipping_price;

//    private BigDecimal shipping_tax_price;

//    private BigDecimal shipping_discount_price;

//    private BigDecimal tax_price;

//    private BigDecimal promotion_discount_price;

//    private BigDecimal gift_wrap_price;

//    private BigDecimal gift_wrap_tax_price;

    private String channel_order_item_id;

//    private BigDecimal price;
//
//    private BigDecimal sales_total_amount;
//
//    private String sku_name;

    private String productSkuCode;

    private String ordernum;
}
