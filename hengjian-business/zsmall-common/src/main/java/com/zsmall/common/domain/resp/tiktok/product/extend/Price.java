package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 17:05
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class Price implements Serializable {
    private String currency;
    @JsonProperty("sale_price")
    @JSONField(name="sale_price")
    private String salePrice;
    @JsonProperty("tax_exclusive_price")
    @JSONField(name="tax_exclusive_price")
    private String taxExclusivePrice;
}
