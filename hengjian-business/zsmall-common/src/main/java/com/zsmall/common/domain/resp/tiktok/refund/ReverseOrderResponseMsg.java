package com.zsmall.common.domain.resp.tiktok.refund;

import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 15:04
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ApiModel(value = "逆向订单接口返回数据", description = "逆向订单返回数据")
public class ReverseOrderResponseMsg extends TikTokResponseBaseEntity {
    @ApiModelProperty(value = "具体返回信息")
    public ReverseOrderResponseData data;
}
