package com.zsmall.common.domain.resp.tiktok.fulfillment;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/1 18:30
 */
@Data
public class SplittableGroup {
    private String id;
    @JsonProperty("order_line_item_ids")
    @JSONField(name = "order_line_item_ids")
    private List<String> orderLineItemIds;

    /**
     * 功能描述：添加订单行项目ID
     *
     * @param orderLineItemIds 订单行项目ID
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/04/02
     */
    public List<String> addOrderLineItemIds(List<String> orderLineItemIds) {
        this.orderLineItemIds = orderLineItemIds;
        return this.orderLineItemIds;

    }
}
