package com.zsmall.common.domain.resp.tiktok.refund;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 14:59
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class RejectReasonDataResp implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("reverse_reason_list")
    @JSONField(name = "reverse_reason_list")
    private List<ReverseReason> reverseReasonList;
}
