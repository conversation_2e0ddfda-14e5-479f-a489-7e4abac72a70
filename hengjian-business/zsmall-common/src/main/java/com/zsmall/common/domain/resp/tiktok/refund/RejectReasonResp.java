package com.zsmall.common.domain.resp.tiktok.refund;

import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 14:59
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class RejectReasonResp extends TikTokResponseBaseEntity {

    private RejectReasonDataResp  data;

}
