package com.zsmall.common.domain.req.base;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 13:40
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Data
@ApiModel(value = "抖音请求数据基础参数", description = "抖音返回数据基础参数")
public class TikTokRequestBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("shop_id")
    @JSONField(name="shop_id")
    private Long shopId;

}
