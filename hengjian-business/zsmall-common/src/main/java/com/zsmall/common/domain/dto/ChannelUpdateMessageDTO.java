package com.zsmall.common.domain.dto;

import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.utils.MessageUtils;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 渠道更新状态信息记录DTO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelUpdateMessageDTO {

    private boolean success;

    private String channelStoreName;

    private String productSkuCode;

    private String message;

    public static ChannelUpdateMessageDTO success(String channelStoreName, String productSkuCode) {
        return new ChannelUpdateMessageDTO(true, channelStoreName, productSkuCode, null);
    }

    public static ChannelUpdateMessageDTO failedMessage(String channelStoreName, String productSkuCode,
        String message) {
        return new ChannelUpdateMessageDTO(false, channelStoreName, productSkuCode, message);
    }

    public String generateIntactMessage(String language) {
        if (this.success) {
            String messageTemplate =
                MessageUtils.message(ZSMallStatusCodeEnum.SALES_CHANNEL_UPDATE_SUCCESS.getMessageCode());
            return StrUtil.format(messageTemplate, this.channelStoreName, this.productSkuCode);
        } else {
            String messageTemplate =
                MessageUtils.message(ZSMallStatusCodeEnum.SALES_CHANNEL_UPDATE_FAILED.getMessageCode());
            return StrUtil.format(messageTemplate, this.channelStoreName, this.productSkuCode, this.message);
        }
    }
}
