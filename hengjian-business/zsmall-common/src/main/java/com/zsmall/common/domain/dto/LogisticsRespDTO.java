package com.zsmall.common.domain.dto;

import com.zsmall.common.enums.order.LogisticsProgress;
import lombok.Data;
import lombok.ToString;

/**
 * 物流响应信息实体
 * <AUTHOR>
 */
@Data
@ToString
public class LogisticsRespDTO {

  /**
   * 承运商
   */
  private Integer carrier;

  /**
   * 响应代号
   */
  private String respCode;

  /**
   * 响应信息
   */
  private String respMessage;

  /**
   * 查询后是否改变物流进度（null则不改变）
   */
  private LogisticsProgress logisticsProgress;

  /**
   * 最后一次物流信息更新时间
   */
  private String activityDateTime;

}
