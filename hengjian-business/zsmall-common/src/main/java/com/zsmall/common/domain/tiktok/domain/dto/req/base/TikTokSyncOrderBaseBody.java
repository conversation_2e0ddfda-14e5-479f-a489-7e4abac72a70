package com.zsmall.common.domain.tiktok.domain.dto.req.base;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 17:12
 */
@NoArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Data
public class TikTokSyncOrderBaseBody implements Serializable {
}
