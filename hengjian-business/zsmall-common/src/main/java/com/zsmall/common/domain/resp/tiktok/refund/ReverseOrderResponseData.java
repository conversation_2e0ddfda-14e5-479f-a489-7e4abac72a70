package com.zsmall.common.domain.resp.tiktok.refund;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 15:04
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
@ApiModel(value = "逆向订单接口返回扩展数据", description = "逆向订单返回数据")
public class ReverseOrderResponseData implements Serializable {

    /**
     * 是否有更多的订单
     */
    public boolean more;
    /**
     * 先显示未处理的订单，然后显示已处理的订单。两组订单分别排序
     */
    @JsonProperty("reverse_list")
    @JSONField(name="reverse_list")
    public List<ReverseData> reverseList;


}
