package com.zsmall.common.domain.tiktok;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.base.TikTokAuthCommonParam;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 19:47
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@SuperBuilder
@Accessors(chain = true)
@ApiModel(value = "tiktokParam-plus", description = "tiktok基础公用参数")
public class TikTokAuthParam extends TikTokAuthCommonParam {

    @JsonProperty("shop_id")
    @JSONField(name = "shop_id")
    private String shopId ;

    @JsonProperty("page_size")
    @JSONField(name="page_size")
    private Integer pageSize;

    @JsonProperty("page_token")
    @JSONField(name="page_token")
    private String pageToken;


}
