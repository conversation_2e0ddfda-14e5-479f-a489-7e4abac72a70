package com.zsmall.common.domain.resp.tiktok.refund;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 15:05
 */

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zsmall.common.domain.tiktok.entity.ReturnItem;
import com.zsmall.common.domain.tiktok.entity.ReverseRecord;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/10 11:10
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
@ApiModel(value = "逆向订单数据", description = "逆向订单数据")
public class ReverseData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 逆向订单 ID
     */
    @JsonProperty("reverse_order_id")
    @JSONField(name="reverse_order_id")
    public String reverseOrderId;

    /**
     * 订单编号
     */
    @JsonProperty("order_id")
    @JSONField(name="order_id")
    public String orderId;

    /**
     * 买方退款总额
     */
    @JsonProperty("refund_total")
    @JSONField(name="refund_total")
    public BigDecimal refundTotal;

    /**
     * 货币类型 IDR
     */
    public String currency;

    /**
     * 反向类型
     * CANCEL=1
     * REFUND_ONLY = 2;
     * RETURN_AND_REFUND = 3;
     * REQUEST_CANCEL = 4;
     */
    @JsonProperty("reverse_type")
    @JSONField(name="")
    public int reverseType;

    /**
     * 退货原因
     * example:Package wasn't received
     */
    @JsonProperty("return_reason")
    @JSONField(name="return_reason")
    public String returnReason;

    @JsonProperty("return_item_list")
    @JSONField(name="return_item_list")
    public List<ReturnItem> returnItemList;

    /**
     * 反向状态值
     * AFTERSALE_APPLYING = 1;
     * AFTERSALE_REJECT_APPLICATION = 2;
     * AFTERSALE_RETURNING = 3;
     * AFTERSALE_BUYER_SHIPPED = 4;
     * AFTERSALE_SELLER_REJECT_RECEIVE = 5;
     * AFTERSALE_SUCCESS = 50;
     * CANCEL_SUCCESS = 51;
     * CLOSED = 99;
     * COMPLETE = 100;
     */
    @JsonProperty("reverse_status_value")
    @JSONField(name="reverse_status_value")
    public int reverseStatusValue;

    /**
     * 反向请求时间
     */
    @JsonProperty("reverse_request_time")
    @JSONField(name="reverse_request_time")
    public Long reverseRequestTime;

    /**
     * 反向更新时间
     */
    @JsonProperty("reverse_update_time")
    @JSONField(name="reverse_update_time")
    public int reverseUpdateTime;

    /**
     * 卖家退货时的退货追踪编码
     */
    @JsonProperty("return_tracking_id")
    @JSONField(name="return_tracking_id")
    public String returnTrackingId;

    @JsonProperty("reverse_record_list")
    @JSONField(name="reverse_record_list")
    public List<ReverseRecord> reverseRecordList;

    /**
     * 返回类型
     * SELF_ARRANGE=1
     * DROP_OFF=2
     * PICK_UP=3
     */
    @JsonProperty("return_type")
    @JSONField(name="return_type")
    public int returnType;


}
