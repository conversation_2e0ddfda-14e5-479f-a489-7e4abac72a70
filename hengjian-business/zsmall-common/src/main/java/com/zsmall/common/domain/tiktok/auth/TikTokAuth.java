package com.zsmall.common.domain.tiktok.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 09:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Accessors(chain = true)
public class TikTokAuth {
    /**
     * 请求TOKEN
     */
    private String access_token;
    /**
     * TOKEN过期时间
     */
    private String access_token_expire_in;

    /**
     * 刷新TOKEN
     */
    private String refresh_token;


    /**
     * 刷新TOKEN 过期时间
     */
    private Long refresh_token_expire_in;

    private String open_id;

    private String seller_name;

    /**
     * 国家
     */
    private String seller_base_region;

    private String user_type;

    private String shop_cipher;


    public TikTokAuth(String accessToken, String refreshToken, String openId, String shopCipher) {
        this.access_token = accessToken;
        this.refresh_token = refreshToken;
        this.open_id = openId;
        this.shop_cipher = shopCipher;
    }

    public TikTokAuth (String accessToken,String refreshToken,String openId) {
        this.access_token = accessToken;
        this.refresh_token = refreshToken;
        this.open_id = openId;
    }
}
