package com.zsmall.common.domain.resp.tiktok.product.extend;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/19 16:52
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class Skus implements Serializable {
    @JsonProperty("external_sku_id")
    @JSONField(name="external_sku_id")
    private String externalSkuId;
    private String id;
    @JsonProperty("identifier_code")
    @JSONField(name="identifier_code")
    private IdentifierCode identifierCode;
    private List<Inventory> inventory;
    private Price price;
    @JsonProperty("sales_attributes")
    @JSONField(name="sales_attributes")
    private List<SalesAttribute> salesAttributes;
    @JsonProperty("seller_sku")
    @JSONField(name="seller_sku")
    private String sellerSku;
}
