package com.zsmall.xxl.job.service;

/**
 * 商品活动相关定时任务-接口
 *
 * <AUTHOR>
 * @date 2023/8/4
 */
public interface ProductActivityJobService {

    /**
     * 仓储费扣除定时任务
     */
    void storageFeeDeductionJob();

    /**
     * 活动定时过期任务
     */
    void activityExpiredJob();

    /**
     * 定时完成活动（分销商）任务
     */
    void activityCompleteDisJob();

    /**
     * 定时完成活动（供应商）任务
     */
    void activityCompleteSupJob();

    /**
     * 定时回收订金任务（分销商已过期活已取消的活动）
     */
    void activityRecoveryDepositJob();

    /**
     * 定时发送临期活动通知（邮件或者短信）
     */
    void activityAdventNoticeJob();

}
