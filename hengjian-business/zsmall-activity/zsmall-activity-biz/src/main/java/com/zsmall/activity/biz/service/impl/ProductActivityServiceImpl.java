package com.zsmall.activity.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.activity.biz.factory.ProductActivityFactory;
import com.zsmall.activity.biz.service.ProductActivityService;
import com.zsmall.activity.biz.support.ProductActivitySupport;
import com.zsmall.activity.entity.domain.*;
import com.zsmall.activity.entity.domain.bo.productActivity.*;
import com.zsmall.activity.entity.domain.dto.ExpiringActivityItemDTO;
import com.zsmall.activity.entity.domain.vo.productActivity.*;
import com.zsmall.activity.entity.iservice.*;
import com.zsmall.activity.entity.util.ActivityCodeGenerator;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.productActivity.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.WalletException;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.extend.utils.ZSMallProductEventUtils;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.TransactionsProductActivityItem;
import com.zsmall.system.entity.domain.event.CheckPaymentPasswordEvent;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.system.entity.iservice.ITransactionsProductActivityItemService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 商品活动相关-业务实现层
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductActivityServiceImpl implements ProductActivityService {

    private final IProductSkuService iProductSkuService;
    private final IProductActivityService iProductActivityService;
    private final IProductActivityPriceService iProductActivityPriceService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IProductActivityReviewRecordService iProductActivityReviewRecordService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductActivityStockService iProductActivityStockService;
    private final IProductActivityStockItemService iProductActivityStockItemService;
    private final IProductActivityStockLockService iProductActivityStockLockService;
    private final IProductActivityStockLockItemService iProductActivityStockLockItemService;
    private final IProductActivityBuyoutService iProductActivityBuyoutService;
    private final IProductActivityBuyoutItemService iProductActivityBuyoutItemService;
    private final IProductActivityCheckoutService iProductActivityCheckoutService;
    private final ITransactionRecordService iTransactionRecordService;
    private final ITransactionsProductActivityItemService iTransactionsProductActivityItemService;
    private final IDownloadRecordService iDownloadRecordService;

    private final TenantWalletService tenantWalletService;
    private final FileProperties fileProperties;

    private final BillSupport billSupport;
    private final ProductActivitySupport productActivitySupport;
    private final ProductActivityFactory productActivityFactory;
    private final ActivityCodeGenerator activityCodeGenerator;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final BusinessParameterService businessParameterService;

    /**
     * 分页查询活动可用商品列表（供应商）
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public TableDataInfo<ProductSkuAndStockVo> getProductList(ProductByActivityBo bo, PageQuery pageQuery) {
        LoginHelper.getLoginUser(TenantType.Supplier);
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();
        IPage<ProductSkuAndStockVo> page = iProductSkuService.queryProductSkuAndStock(queryType, queryValue, pageQuery.build());
        return TableDataInfo.build(page);
    }

    /**
     * 分页查询商品活动列表（自动分发至各角色不同处理逻辑）
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return productActivityFactory.getService(loginUser.getTenantTypeEnum()).getProductActivityPage(bo, pageQuery);
    }

    /**
     * 创建活动
     *
     * @param bo
     */
    @Override
    public R<Void> createActivity(ProductActivityBaseBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        return productActivityFactory.getService(loginUser.getTenantTypeEnum()).createActivity(bo);
    }

    /**
     * 查询商品活动草稿
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDraftVo> getActivityDraft(String activityID) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        return productActivityFactory.getService(loginUser.getTenantTypeEnum()).getActivityDraft(activityID);
    }

    /**
     * 查询商品活动详情（平台、供货商）
     *
     * @param activityID
     */
    @Override
    public R<ProductActivityDetailBaseVo> getActivityDetail(String activityID) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return productActivityFactory.getService(loginUser.getTenantTypeEnum()).getActivityDetail(activityID);
    }

    /**
     * 锁货转圈货
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = { Exception.class, RStatusCodeException.class })
    public R<Void> stockLockToBuyout(StockLockToBuyoutBo bo) {
        LoginUser manager = LoginHelper.getLoginUser(TenantType.Distributor);
        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(bo.getPaymentPassword()));

        // 查询请求参数中的活动是否存在
        ProductActivityItem productActivityItem = iProductActivityItemService.queryOneByEntity(
            ProductActivityItem.builder().activityCode(bo.getActivityID()).activityType(ProductActivityTypeEnum.StockLock).build()
        );

        if (productActivityItem == null) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
        }

        if (!ProductActivityItemStateEnum.InProgress.equals(productActivityItem.getActivityState())) {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(bo.getActivityID()));
        }

        // 根据活动id查询锁货详情
        Long productActivityItemId = productActivityItem.getId();
        ProductActivityStockLockItem stockLockItem =
            iProductActivityStockLockItemService.queryByActivityItemId(productActivityItemId);
        Long stockLockItemId = stockLockItem.getId();

        // 锁货剩余
        Integer stockLockSurplus = stockLockItem.getStockLockSurplus();
        // 锁货已售数量
        Integer stockLockSold = stockLockItem.getStockLockSold();

        // 原来的供货商锁货活动
        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(productActivityItem.getActivityCodeParent()).build());
        log.info("创建圈货活动（锁货转圈货） 原来的供货商锁货活动 = {}", JSONUtil.toJsonStr(productActivity));
        Long productActivityId = productActivity.getId();

        // 查询原来的定价信息
        ProductActivityPrice oldActivityPrice_SUP =
            iProductActivityPriceService.queryOneByEntity(ProductActivityPrice.builder().productActivityId(productActivityId).build());
        log.info("创建圈货活动（锁货转圈货） 原来的锁货定价信息 = {}", JSONUtil.toJsonStr(oldActivityPrice_SUP));

        // 新建一个供货商圈货活动
        ProductActivity newProductActivity_SUP = new ProductActivity();
        newProductActivity_SUP.setActivityCode(activityCodeGenerator.codeGenerate(BusinessCodeEnum.ProductActivityBuyout));
        newProductActivity_SUP.setActivityState(ProductActivityStateEnum.InProgress);
        newProductActivity_SUP.setActivityType(ProductActivityTypeEnum.Buyout);
        newProductActivity_SUP.setActivityName(productActivity.getActivityName() + "(Buyout)");
        newProductActivity_SUP.setProductName(productActivityItem.getProductName());
        newProductActivity_SUP.setProductSku(productActivityItem.getProductSku());
        newProductActivity_SUP.setProductImg(productActivityItem.getProductImg());
        newProductActivity_SUP.setProductSkuCode(productActivityItem.getProductSkuCode());
        newProductActivity_SUP.setProductCode(productActivityItem.getProductCode());
        newProductActivity_SUP.setQuantityTotal(stockLockSurplus);
        newProductActivity_SUP.setQuantityAlready(stockLockSurplus);
        newProductActivity_SUP.setQuantityMinimum(stockLockSurplus);
        newProductActivity_SUP.setQuantitySurplus(0);
        newProductActivity_SUP.setTenantId(productActivity.getTenantId());

        // 保存供货商新活动信息
        log.info("创建圈货活动（锁货转圈货） 供货商新活动信息newProductActivity_SUP = {}", JSONUtil.toJsonStr(newProductActivity_SUP));
        TenantHelper.ignore(() -> iProductActivityService.save(newProductActivity_SUP));

        Long newActivityId_SUP = newProductActivity_SUP.getId();
        String newActivityCode_SUP = newProductActivity_SUP.getActivityCode();

        // 拷贝锁货库存分配给供货商的新圈货
        List<ProductActivityStockItem> stockLockStockList =
            iProductActivityStockItemService.getEnoughByItemId(productActivityItemId, 0);
        log.info("创建圈货活动（锁货转圈货）分销商原来的锁货仓库信息stockLockInventories = {}", JSONUtil.toJsonStr(stockLockStockList));

        List<ProductActivityStock> buyoutStockList = new ArrayList<>();
        for (ProductActivityStockItem stockLockInventory : stockLockStockList) {
            ProductActivityStock buyoutInventory = new ProductActivityStock();
            buyoutInventory.setActivityCode(newActivityCode_SUP);
            buyoutInventory.setProductActivityId(newActivityId_SUP);
            buyoutInventory.setWarehouseSystemCode(stockLockInventory.getWarehouseSystemCode());
            buyoutInventory.setQuantitySold(0);
            buyoutInventory.setQuantityTotal(stockLockInventory.getQuantitySurplus());
            buyoutInventory.setQuantitySurplus(0);
            buyoutStockList.add(buyoutInventory);
        }
        log.info("创建圈货活动（锁货转圈货）供货商新的仓库信息buyoutInventories = {}", JSONUtil.toJsonStr(buyoutStockList));
        TenantHelper.ignore(() -> iProductActivityStockService.saveBatch(buyoutStockList));

        // 新建一个供货商圈货活动详情
        ProductActivityBuyout newBuyout_SUP = new ProductActivityBuyout();
        newBuyout_SUP.setActivityCode(newActivityCode_SUP);
        newBuyout_SUP.setProductActivityId(newActivityId_SUP);
        newBuyout_SUP.setBuyoutQuantityTotal(stockLockSurplus);
        newBuyout_SUP.setBuyoutQuantityMinimum(stockLockSurplus);
        newBuyout_SUP.setBuyoutQuantityAlready(stockLockSurplus);
        newBuyout_SUP.resetSurplus();

        // 保存供货商新活动圈货信息
        log.info("创建圈货活动（锁货转圈货） 供货商新活动圈货信息newBuyout_SUP = {}", JSONUtil.toJsonStr(newBuyout_SUP));
        TenantHelper.ignore(() -> iProductActivityBuyoutService.save(newBuyout_SUP));

        Long newBuyoutId_SUP = newBuyout_SUP.getId();

        // 拷贝一份给新的圈货用
        ProductActivityPrice newActivityPrice_SUP =
            BeanUtil.toBean(oldActivityPrice_SUP, ProductActivityPrice.class);
        newActivityPrice_SUP.setId(null);
        newActivityPrice_SUP.setActivityCode(newActivityCode_SUP);
        newActivityPrice_SUP.setProductActivityId(newActivityId_SUP);

        BigDecimal activityUnitPrice = oldActivityPrice_SUP.getActivityUnitPrice();
        BigDecimal activityOperationFee = oldActivityPrice_SUP.getActivityOperationFee();
        BigDecimal activityPickUpPrice = oldActivityPrice_SUP.getActivityPickUpPrice();
        BigDecimal activityDropShippingPrice = oldActivityPrice_SUP.getActivityDropShippingPrice();

        newActivityPrice_SUP.setActivityUnitPrice(activityUnitPrice);
        // 因为原来的尾款中已经包含了操作费，所以此处要设置为0
        newActivityPrice_SUP.setActivityOperationFee(activityOperationFee);
        // 活动自提价
        newActivityPrice_SUP.setActivityPickUpPrice(activityPickUpPrice);
        // 活动代发价（活动单价+活动操作费+活动尾程派送费）
        newActivityPrice_SUP.setActivityDropShippingPrice(activityDropShippingPrice);

        // 活动订金比例
        JSONObject ACTIVITY_DEPOSIT_PRICE_PERCENT = businessParameterService.getValueFromJSONObject(BusinessParameterType.ACTIVITY_DEPOSIT_PRICE_PERCENT);
        BigDecimal depositPricePercent = new BigDecimal(ACTIVITY_DEPOSIT_PRICE_PERCENT.getStr(ProductActivityTypeEnum.Buyout.name()));

        // 活动订金单价（活动自提价*一定比例后得出）
        BigDecimal newActivityDepositUnitPrice = NumberUtil.mul(activityPickUpPrice, depositPricePercent);
        // 活动尾款单价（活动自提价-活动订金单价后得出）
        BigDecimal newActivityBalanceUnitPrice = NumberUtil.sub(activityPickUpPrice, newActivityDepositUnitPrice);
        // 活动订金总价（活动订金单价*数量得出）
        BigDecimal activityDepositTotalPrice = NumberUtil.mul(newActivityDepositUnitPrice, stockLockSurplus);
        newActivityPrice_SUP.setActivityDepositUnitPrice(newActivityDepositUnitPrice);
        newActivityPrice_SUP.setActivityBalanceUnitPrice(newActivityBalanceUnitPrice);
        newActivityPrice_SUP.setActivityDepositTotalPrice(activityDepositTotalPrice);

        BigDecimal platformUnitPrice = oldActivityPrice_SUP.getPlatformUnitPrice();
        BigDecimal platformOperationFee = oldActivityPrice_SUP.getPlatformOperationFee();
        BigDecimal platformPickUpPrice = oldActivityPrice_SUP.getPlatformPickUpPrice();
        BigDecimal platformDropShippingPrice = oldActivityPrice_SUP.getPlatformDropShippingPrice();
        // // 平台订金单价
        // BigDecimal platformDepositUnitPrice = oldActivityPrice_SUP.getPlatformDepositUnitPrice();
        // 锁货的尾款单价，需要分销商本次付款结清
        BigDecimal platformBalanceUnitPrice = oldActivityPrice_SUP.getPlatformBalanceUnitPrice();
        // BigDecimal newPlatformUnitPrice = NumberUtil.add(platformDepositUnitPrice, platformBalanceUnitPrice);

        newActivityPrice_SUP.setPlatformUnitPrice(platformUnitPrice);
        // 因为原来的尾款中已经包含了操作费，所以此处要设置为0
        newActivityPrice_SUP.setPlatformOperationFee(platformOperationFee);
        // 平台自提价，包含锁货的订金
        newActivityPrice_SUP.setPlatformPickUpPrice(platformPickUpPrice);
        // 平台代发价（平台单价+平台操作费+平台尾程派送费，包含锁货的订金）
        newActivityPrice_SUP.setPlatformDropShippingPrice(platformDropShippingPrice);

        // 平台订金单价（平台自提价*一定比例后得出，包含锁货的订金）
        BigDecimal newPlatformDepositUnitPrice = NumberUtil.mul(platformPickUpPrice, depositPricePercent);
        // 平台尾款单价（平台自提价-平台订金单价后得出，包含锁货的订金）
        BigDecimal newPlatformBalanceUnitPrice = NumberUtil.sub(platformPickUpPrice, newPlatformDepositUnitPrice);
        // 平台订金总价（平台订金单价*数量得出，包含锁货的订金）
        BigDecimal platformDepositTotalPrice = NumberUtil.mul(newPlatformDepositUnitPrice, stockLockSurplus);

        // 平台订金单价（平台自提价*一定比例后得出，不包含锁货的订金）
        BigDecimal newPlatformDepositUnitPrice_NotStockLock = NumberUtil.mul(platformBalanceUnitPrice, depositPricePercent);
        // 平台订金总价（平台订金单价*数量得出，不包含锁货的订金，用于分销商支付）
        BigDecimal platformDepositTotalPrice_NotStockLock = NumberUtil.mul(newPlatformDepositUnitPrice_NotStockLock, stockLockSurplus);

        newActivityPrice_SUP.setPlatformDepositUnitPrice(newPlatformDepositUnitPrice);
        newActivityPrice_SUP.setPlatformBalanceUnitPrice(newPlatformBalanceUnitPrice);
        newActivityPrice_SUP.setPlatformDepositTotalPrice(platformDepositTotalPrice);

        // 保存
        log.info("创建圈货活动（锁货转圈货） 供货商新圈货定价信息newActivityPrice_SUP = {}", JSONUtil.toJsonStr(newActivityPrice_SUP));
        TenantHelper.ignore(() -> iProductActivityPriceService.save(newActivityPrice_SUP));
        Long newActivityPriceId_SUP = newActivityPrice_SUP.getId();

        // 分销商活动
        ProductActivityItem newProductActivityItem = new ProductActivityItem();
        newProductActivityItem.setActivityCode(activityCodeGenerator.codeGenerate(BusinessCodeEnum.ProductActivityBuyout, newActivityCode_SUP));
        newProductActivityItem.setActivityState(ProductActivityItemStateEnum.InProgress);
        newProductActivityItem.setActivityType(ProductActivityTypeEnum.Buyout);
        newProductActivityItem.setProductName(productActivityItem.getProductName());
        newProductActivityItem.setProductSku(productActivityItem.getProductSku());
        newProductActivityItem.setProductImg(productActivityItem.getProductImg());
        newProductActivityItem.setProductSkuCode(productActivityItem.getProductSkuCode());
        newProductActivityItem.setProductCode(productActivityItem.getProductCode());
        newProductActivityItem.setActivityCodeParent(newActivityCode_SUP);
        newProductActivityItem.setQuantityTotal(stockLockSurplus);
        newProductActivityItem.setQuantitySurplus(stockLockSurplus);

        log.info("创建圈货活动（锁货转圈货） 分销商新活动信息newProductActivityItem = {}", JSONUtil.toJsonStr(newProductActivityItem));
        iProductActivityItemService.save(newProductActivityItem);
        Long newProductActivityItemId = newProductActivityItem.getId();
        String newActivityCode_DIS = newProductActivityItem.getActivityCode();

        // 圈货活动详情
        ProductActivityBuyoutItem buyoutItem = new ProductActivityBuyoutItem();
        buyoutItem.setActivityCode(newActivityCode_DIS);
        buyoutItem.setActivityCodeParent(newActivityCode_SUP);
        buyoutItem.setProductActivityItemId(newProductActivityItemId);
        buyoutItem.setProductActivityStockLockItemId(stockLockItemId);
        buyoutItem.setBuyoutSold(0);
        buyoutItem.setBuyoutQuantity(stockLockSurplus);
        buyoutItem.setBuyoutSurplus(stockLockSurplus);

        log.info("创建圈货活动（锁货转圈货） 分销商新圈货信息buyoutItem = {}", JSONUtil.toJsonStr(buyoutItem));
        iProductActivityBuyoutItemService.save(buyoutItem);

        // 拷贝锁货库存分配
        List<ProductActivityStockItem> buyoutItemInventories = new ArrayList<>();
        for (ProductActivityStockItem stockLockStock : stockLockStockList) {
            ProductActivityStockItem buyoutItemStock = new ProductActivityStockItem();
            buyoutItemStock.setActivityCode(newActivityCode_DIS);
            buyoutItemStock.setProductActivityItemId(newProductActivityItemId);
            buyoutItemStock.setWarehouseSystemCode(stockLockStock.getWarehouseSystemCode());
            buyoutItemStock.setQuantitySold(0);
            buyoutItemStock.setQuantityTotal(stockLockStock.getQuantitySurplus());
            buyoutItemStock.setQuantitySurplus(stockLockStock.getQuantitySurplus());
            buyoutItemInventories.add(buyoutItemStock);
        }

        // 保存圈货库存分配
        log.info("创建圈货活动（锁货转圈货） 分销商新圈货库存信息buyoutInventories = {}", JSONUtil.toJsonStr(buyoutItemInventories));
        iProductActivityStockItemService.saveBatch(buyoutItemInventories);

        // 分销商新活动的定价信息
        ProductActivityPriceItem newActivityItemPrice_Dis = BeanUtil.toBean(newActivityPrice_SUP, ProductActivityPriceItem.class);

        // 活动总价（活动自提价*锁货剩余数量得出）
        BigDecimal activityTotalPrice = NumberUtil.mul(newActivityPrice_SUP.getActivityPickUpPrice(), stockLockSurplus).setScale(2, RoundingMode.HALF_UP);
        BigDecimal platformTotalPrice = NumberUtil.mul(newActivityPrice_SUP.getPlatformPickUpPrice(), stockLockSurplus).setScale(2, RoundingMode.HALF_UP);

        newActivityItemPrice_Dis.setId(null);
        newActivityItemPrice_Dis.setActivityCode(newActivityCode_DIS);
        newActivityItemPrice_Dis.setActivityCodeParent(newActivityCode_SUP);
        newActivityItemPrice_Dis.setProductActivityItemId(newProductActivityItemId);
        newActivityItemPrice_Dis.setActivityDepositTotalPrice(activityDepositTotalPrice);
        newActivityItemPrice_Dis.setActivityDepositSurplusPrice(activityDepositTotalPrice);
        newActivityItemPrice_Dis.setActivityTotalPrice(activityTotalPrice);
        newActivityItemPrice_Dis.setPlatformDepositTotalPrice(platformDepositTotalPrice);
        newActivityItemPrice_Dis.setPlatformDepositSurplusPrice(platformDepositTotalPrice);
        newActivityItemPrice_Dis.setPlatformTotalPrice(platformTotalPrice);
        log.info("创建圈货活动（锁货转圈货） 分销商新圈货定价信息newActivityItemPrice_Bulk = {}", JSONUtil.toJsonStr(newActivityItemPrice_Dis));
        iProductActivityPriceItemService.save(newActivityItemPrice_Dis);

        // 原先的锁货活动改为已完成
        productActivityItem.setActivityState(ProductActivityItemStateEnum.Completed);
        productActivityItem.setQuantitySurplus(0);
        productActivityItem.setQuantitySold(NumberUtil.add(stockLockSold, stockLockSurplus).intValue());

        // 原来的锁货活动，剩余数量设置为0，数量改为全部售出
        stockLockItem.setStockLockSurplus(0);
        stockLockItem.setStockLockSold(NumberUtil.add(stockLockSold, stockLockSurplus).intValue());
        // 清空库存详情的关联的所有剩余库存
        for (ProductActivityStockItem stockLockStock : stockLockStockList) {
            Integer stockLockSold_inventory = stockLockStock.getQuantitySold();
            Integer stockLockSurplus_inventory = stockLockStock.getQuantitySurplus();
            stockLockStock.setQuantitySold(NumberUtil.add(stockLockSold_inventory, stockLockSurplus_inventory).intValue());
            stockLockStock.setQuantitySurplus(0);
        }
        iProductActivityItemService.updateById(productActivityItem);
        iProductActivityStockLockItemService.updateById(stockLockItem);
        iProductActivityStockItemService.updateBatchById(stockLockStockList);

        try {
            // 分销商需要支付的买断费用
            log.info("分销商需要支付的买断费用 = {}", platformDepositTotalPrice_NotStockLock);

            TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
            transactionRecord.setTransactionSubType(TransactionSubTypeEnum.StockLockBuyoutFee);
            transactionRecord.setTransactionState(TransactionStateEnum.Processing);
            transactionRecord.setTransactionAmount(platformDepositTotalPrice_NotStockLock);
            tenantWalletService.walletChanges(transactionRecord);

            TransactionsProductActivityItem transactionsActivityItem = new TransactionsProductActivityItem();
            transactionsActivityItem.setTransactionsId(transactionRecord.getId());
            transactionsActivityItem.setProductActivityItemId(newProductActivityItemId);
            iTransactionsProductActivityItemService.save(transactionsActivityItem);
        } catch (WalletException e) {
            log.info("【锁货转圈货】钱包支付时出现异常（WalletException） {}", e.getMessage(), e);
            LocaleMessage localeMessage = e.getLocaleMessage();
            throw new RStatusCodeException(localeMessage.toMessage());
        } catch (Exception e) {
            log.info("【锁货转圈货】钱包支付时出现未知异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYMENT_FAILED);
        }
        return R.ok();
    }

    /**
     * 更新活动状态
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> updateState(ActivityUpdateBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return productActivityFactory.getService(loginUser.getTenantTypeEnum()).updateState(bo);
    }

    /**
     * 分页查询临期商品活动列表
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public TableDataInfo<ExpiringActivityItemQueryVo> queryExpiringActivityPage(ExpiringActivityQueryBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager, TenantType.Distributor);
        String tenantId = TenantType.Distributor.equals(loginUser.getTenantTypeEnum()) ? loginUser.getTenantId() : null;
        ExpiringActivityItemDTO dto = productActivitySupport.buildExpiringActivityItemDTO(tenantId, bo.getQueryType(), bo.getQueryValue(), bo.getEndDay());
        IPage<ExpiringActivityItemQueryVo> resultPage = iProductActivityItemService.getExpiringActivityItem(dto, pageQuery.build());
        return TableDataInfo.build(resultPage);
    }

    /**
     * 查询分销商活动库存信息
     *
     * @param bo
     */
    @Override
    public R<ActivityStockDVo> queryActivityStockItem(String activityID) {
        LoginHelper.getLoginUser(TenantType.Manager);

        ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityID).activityState(ProductActivityItemStateEnum.InProgress).build());
        ActivityStockDVo vo = new ActivityStockDVo();

        if (ObjectUtil.isNotNull(activityItem)) {
            Long activityItemId = activityItem.getId();
            String activityCodeParent = activityItem.getActivityCodeParent();

            List<ProductActivityStockItem> stockItemList =
                iProductActivityStockItemService.queryByActivityItemId(activityItemId);

            for (ProductActivityStockItem stockItem : stockItemList) {
                String warehouseSystemCode = stockItem.getWarehouseSystemCode();
                Integer quantityTotal = stockItem.getQuantityTotal();
                Integer quantitySold = stockItem.getQuantitySold();

                // 使用主活动编号查询主活动的库存信息
                ProductActivityStock activityStock =
                    iProductActivityStockService.queryByActivityCodeAndWarehouseSysCode(activityCodeParent, warehouseSystemCode);
                Integer quantitySurplus = activityStock.getQuantitySurplus();
                Integer quantityMax = quantityTotal + quantitySurplus;

                vo.addInventory(warehouseSystemCode, quantityMax, quantityTotal, quantitySurplus, quantitySold);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST_OR_CANCELLED);
        }
        return R.ok(vo);
    }

    /**
     * 分页查询活动资金信息（管理员）
     */
    @Override
    public ProductActivityPriceTableVo<ProductActivityPriceVo> queryActivityPricePage(ProductActivityPriceBo bo, PageQuery pageQuery) {
        LoginUser manager = LoginHelper.getLoginUser(TenantType.Manager);
        IPage<ProductActivityPriceVo> iPage = iProductActivityService.queryActivityPricePage(bo, pageQuery.build());

        TableDataInfo<ProductActivityPriceVo> tableDataInfo = TableDataInfo.build(iPage);
        ProductActivityPriceTableVo<ProductActivityPriceVo> vo = BeanUtil.toBean(tableDataInfo, ProductActivityPriceTableVo.class);
        vo.setPlatformTotalAmount(iProductActivityService.sumPlatformTotalAmount());
        return vo;
    }

    /**
     * 导出活动资金信息（管理员）
     *
     * @param bo
     */
    @Override
    public R<Void> exportActivityPrice(ProductActivityPriceBo bo) {
        LoginUser manager = LoginHelper.getLoginUser(TenantType.Manager);

        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {

            String fileName = StrUtil.format(FileNameConstants.PRODUCT_ACTIVITY_PRICE, DateUtil.format(new Date(), "yyMMdd-HH:mm:ss.SSSS"));

            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(DownloadTypePlusEnum.ProductActivityPrice);
            iDownloadRecordService.save(newRecord);

            ThreadUtil.execute(() -> {
                try {
                    IPage<ProductActivityPriceVo> iPage = iProductActivityService.queryActivityPricePage(bo, new PageQuery().build());
                    List<ProductActivityPriceVo> list = iPage.getRecords();

                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFile = tempSavePath + File.separator + UUID.fastUUID().toString(true) + ".xlsx";

                    BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                    ExcelUtil.exportExcelWithLocale(list, "ActivityPrice", ProductActivityPriceVo.class, outputStream);
                    outputStream.close();
                    BufferedInputStream inputStream = FileUtil.getInputStream(tempFile);

                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    // newRecord.setFileSize(StrUtil.toString(inputStream.length()));
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);
                    inputStream.close();
                    FileUtil.del(tempFile);
                } catch (Exception e) {
                    log.error("【导出活动资金信息】出现未知错误 {}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                }
            });
        }
        return R.ok();
    }

    /**
     * 取消分销商活动（管理员）
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> cancelActivityByManager(CancelBulkActivityBo bo) throws Exception {
        LoginUser manager = LoginHelper.getLoginUser(TenantType.Manager);

        String activityID = bo.getActivityID();
        BigDecimal refundAmount = bo.getRefundAmount();
        BigDecimal deductionAmount = bo.getDeductionAmount();
        String remark = bo.getRemark();

        ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityID).activityState(ProductActivityItemStateEnum.InProgress).build());

        if (ObjectUtil.isNotNull(activityItem)) {
            ProductActivityTypeEnum activityType = activityItem.getActivityType();
            // 分销商编号
            String dTenantId = activityItem.getTenantId();
            Long activityItemId = activityItem.getId();

            // 查询分销商活动定价数据
            ProductActivityPriceItem activityPriceItem =
                iProductActivityPriceItemService.queryByActivityItemId(activityItemId);
            BigDecimal platformTotalPrice = activityPriceItem.getPlatformTotalPrice();
            if (NumberUtil.isGreater(refundAmount, platformTotalPrice)) {
                return R.fail(ZSMallStatusCodeEnum.REFUND_AMOUNT_CANNOT_BE_GREATER_THAN.args(DecimalUtil.bigDecimalToString(platformTotalPrice)));
            }

            BigDecimal activityTotalPrice = activityPriceItem.getActivityTotalPrice();
            if (NumberUtil.isGreater(deductionAmount, activityTotalPrice)) {
                return R.fail(ZSMallStatusCodeEnum.DEDUCTION_AMOUNT_CANNOT_BE_GREATER_THAN.args(DecimalUtil.bigDecimalToString(activityTotalPrice)));
            }

            BigDecimal activityDepositUnitPrice = activityPriceItem.getActivityDepositUnitPrice();
            BigDecimal activityDepositSurplusPrice = activityPriceItem.getActivityDepositSurplusPrice();

            BigDecimal platformDepositUnitPrice = activityPriceItem.getPlatformDepositUnitPrice();
            BigDecimal platformDepositSurplusPrice = activityPriceItem.getPlatformDepositSurplusPrice();

            // 获取剩余数量
            Integer surplusQuantity = activityItem.getQuantitySurplus();

            // 查询归属的供货商活动
            ProductActivity productActivity =
                iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityItem.getActivityCodeParent()).build());
            Long activityId = productActivity.getId();
            // 供货商编号
            String sTenantId = productActivity.getTenantId();

            // 生成供货商支出
            TransactionRecord sTransactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            sTransactionRecord.setTenantId(sTenantId);
            sTransactionRecord.setTransactionAmount(deductionAmount);
            sTransactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
            sTransactionRecord.setTransactionSubType(TransactionSubTypeEnum.PlatformDeduct);
            sTransactionRecord.setTransactionState(TransactionStateEnum.Success);
            sTransactionRecord.setTransactionNote(remark);

            TransactionRecord dTransactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            dTransactionRecord.setTenantId(dTenantId);
            dTransactionRecord.setTransactionAmount(refundAmount);
            dTransactionRecord.setTransactionType(TransactionTypeEnum.Income);
            dTransactionRecord.setTransactionSubType(TransactionSubTypeEnum.ProductActivityRefund);
            dTransactionRecord.setTransactionState(TransactionStateEnum.Processing);
            dTransactionRecord.setTransactionNote(activityID);
            // tenantWalletService.walletChanges(orderRefund.getTenantId(), transactionRecord);

            //记录扣除的订金到结算表
            ProductActivityCheckout checkout = new ProductActivityCheckout();
            checkout.setSupplierTenantId(sTenantId);
            checkout.setDistributorTenantId(dTenantId);
            checkout.setProductActivityId(activityId);
            checkout.setProductActivityItemId(activityItemId);
            checkout.setCheckoutType(ActivityCheckoutTypeEnum.PenaltyFee);
            checkout.setCheckoutUnitPrice(activityDepositUnitPrice);
            checkout.setCheckoutAmount(activityDepositSurplusPrice);
            checkout.setPlatformCheckoutUnitPrice(platformDepositUnitPrice);
            checkout.setPlatformCheckoutAmount(platformDepositSurplusPrice);
            checkout.setCheckoutQuantity(surplusQuantity);
            checkout.setDistributorPay(CheckoutPayEnum.Paid);
            iProductActivityCheckoutService.save(checkout);

            RedissonClient client = RedisUtils.getClient();
            String key = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_ITEM_LOCK + activityID;
            RLock lock = client.getLock(key);

            try {
                lock.lock(10L, TimeUnit.SECONDS);
                // 持锁后，取消该分销商的活动
                log.info("子活动{} 加锁成功", activityID);

                // 记收入账
                billSupport.generateBillDTOByProductActivityCheckout(checkout, null);
                // 记支出账
                TenantHelper.ignore(() -> iTransactionRecordService.save(sTransactionRecord));
                billSupport.generateBillDTOByRechargeOrDeduct(manager.getTenantId(), sTransactionRecord);

                // 直接充入分销商钱包
                tenantWalletService.walletChanges(dTenantId, dTransactionRecord, true);

                iTransactionsProductActivityItemService.saveRelation(dTransactionRecord.getId(), activityItemId);

                // 将活动的库存全部置为0
                activityItem.setActivityState(ProductActivityItemStateEnum.Canceled);
                activityItem.setQuantitySurplus(0);

                // 剩余定金也置为0
                activityPriceItem.setActivityDepositSurplusPrice(BigDecimal.ZERO);
                activityPriceItem.setPlatformDepositSurplusPrice(BigDecimal.ZERO);

                List<ProductActivityStockItem> stockItemList = iProductActivityStockItemService.queryByActivityItemId(activityItemId);
                stockItemList.forEach(stockItem -> stockItem.setQuantitySurplus(0));

                TenantHelper.ignore(() -> {
                    iProductActivityItemService.updateById(activityItem);
                    iProductActivityStockItemService.updateBatchById(stockItemList);
                    iProductActivityPriceItemService.updateById(activityPriceItem);
                });
                iProductActivityStockLockItemService.updateSurplusByActivityItemId(activityItemId, 0);
                iProductActivityBuyoutItemService.updateSurplusByActivityItemId(activityItemId, 0);
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                log.info("子活动{} 解锁成功", activityID);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST_OR_CANCELLED);
        }
        return R.ok();
    }

    /**
     * 调整分销商活动（管理员）
     *
     * @param bo
     * @param request
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> adjustActivityByManager(AdjustActivityDBo bo, HttpServletRequest request) throws Exception {

        Map<String, Integer> stockAdjustMap = bo.getStockAdjustMap();
        String activityCode = bo.getActivityCode();
        AdjustActivityDBo.ProcessMethod processMethod = bo.getProcessMethod();
        BigDecimal differencePrice = bo.getDifferencePrice();
        String remark = bo.getRemark();

        if (StrUtil.hasBlank(activityCode, remark) || ObjectUtil.hasNull(processMethod, differencePrice) || CollUtil.isEmpty(stockAdjustMap)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode).activityState(ProductActivityItemStateEnum.InProgress).build());

        if (ObjectUtil.isNotNull(activityItem)) {
            Long activityItemId = activityItem.getId();
            String dTenantId = activityItem.getTenantId();
            String activityCodeParent = activityItem.getActivityCodeParent();

            if (ProductActivityTypeEnum.Buyout.equals(activityItem.getActivityType())) {
                boolean stockLockToBuyout = iProductActivityItemService.isStockLockToBuyout(activityItemId);
                if (stockLockToBuyout) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.ADJUSTING_INVENTORY_NOT_SUPPORTED);
                }
            }

            // 查询分销商库存信息
            List<ProductActivityStockItem> stockItemList =
                iProductActivityStockItemService.queryByActivityItemId(activityItemId);

            // 准备交易记录相关类型
            TransactionSubTypeEnum transactionSubType = TransactionSubTypeEnum.ProductActivityDeduction;
            TransactionTypeEnum transactionType = TransactionTypeEnum.Expenditure;
            if (AdjustActivityDBo.ProcessMethod.Refund.equals(processMethod)) {
                transactionType = TransactionTypeEnum.Income;
                transactionSubType = TransactionSubTypeEnum.ProductActivityRefund;
            }

            // 准备分销商的收入/支出记录
            TransactionRecord dTransactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            dTransactionRecord.setTenantId(dTenantId);
            dTransactionRecord.setTransactionAmount(differencePrice);
            dTransactionRecord.setTransactionType(transactionType);
            dTransactionRecord.setTransactionSubType(transactionSubType);
            dTransactionRecord.setTransactionState(TransactionStateEnum.Processing);
            dTransactionRecord.setTransactionNote(remark);

            // 查询归属的供货商活动
            ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityCodeParent).build());
            Long productActivityId = productActivity.getId();
            String productSkuCode = productActivity.getProductSkuCode();
            ProductActivityTypeEnum activityType = productActivity.getActivityType();

            RedissonClient client = RedisUtils.getClient();
            String key = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_LOCK + activityCodeParent;
            RLock lock = client.getLock(key);

            TimeInterval timer = DateUtil.timer();
            try {
                lock.lock(10L, TimeUnit.SECONDS);
                log.info("主商品活动{}加锁成功", key);

                // 主活动的库存信息
                List<ProductActivityStock> stockList = new ArrayList<>();

                Integer quantityTotalBulk = 0;
                // 库存数量总差额
                Integer differenceValueTotal = 0;
                // 调整前的库存信息，作日志用
                Map<String, Integer> beforeInventoryAdjustMap = new HashMap<>();
                for (ProductActivityStockItem stockItem : stockItemList) {
                    String warehouseSystemCode = stockItem.getWarehouseSystemCode();
                    // 之前分销商锁的库存总数
                    Integer beforeQuantityTotal = stockItem.getQuantityTotal();
                    // 已售出数
                    Integer quantitySold = stockItem.getQuantitySold();
                    Integer afterQuantityTotal = stockAdjustMap.get(warehouseSystemCode);
                    beforeInventoryAdjustMap.put(warehouseSystemCode, beforeQuantityTotal);
                    if (afterQuantityTotal == null) {
                        continue;
                    }
                    // 调整后的库存数量，不能小于已售出的库存数量
                    if (NumberUtil.compare(afterQuantityTotal, quantitySold) < 0) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.ADJUST_INVENTORY_CANNOT_BE_LESS_THAN.args(warehouseSystemCode,
                            NumberUtil.toStr(afterQuantityTotal), NumberUtil.toStr(quantitySold)));
                    }

                    Integer differenceValue = afterQuantityTotal - beforeQuantityTotal;
                    log.info("供货商活动id = {}，分销商活动id = {}，仓库 = {}， 库存差值 = {}", productActivityId, activityItemId, warehouseSystemCode, differenceValue);

                    if (differenceValue != 0) {  // 差值不等于零的才做操作，等于零的说明没改
                        ProductActivityStock activityStock =
                            iProductActivityStockService.queryByActivityIdAndWarehouseSysCode(productActivityId, warehouseSystemCode);

                        // 供货商剩余库存
                        Integer quantitySurplus = activityStock.getQuantitySurplus();
                        if (differenceValue > 0 && quantitySurplus < differenceValue) {
                            log.info("活动库存不足");
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.STOCK_LOCK_QUANTITY_NOT_ENOUGH.args(warehouseSystemCode));
                        }

                        // 分销商剩余库存
                        Integer itemQuantitySurplus = stockItem.getQuantitySurplus();
                        Integer newItemQuantitySurplus = itemQuantitySurplus + differenceValue;
                        if (newItemQuantitySurplus < 0) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.ADJUST_INVENTORY_CANNOT_BE_LESS_THAN_ZERO.args(warehouseSystemCode));
                        }
                        stockItem.setQuantityTotal(afterQuantityTotal);
                        stockItem.setQuantitySurplus(newItemQuantitySurplus);

                        differenceValueTotal += differenceValue;
                        quantityTotalBulk += afterQuantityTotal;
                        quantitySurplus -= differenceValue;
                        activityStock.setQuantitySurplus(quantitySurplus);
                        stockList.add(activityStock);
                    }
                }

                if (CollUtil.isEmpty(stockList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.INVENTORY_UNCHANGED);
                }

                ProductActivityPriceItem activityItemPrice = iProductActivityPriceItemService.queryByActivityItemId(activityItemId);
                BigDecimal activityDepositSurplusPrice = activityItemPrice.getActivityDepositSurplusPrice();
                BigDecimal activityDepositUnitPrice = activityItemPrice.getActivityDepositUnitPrice();
                BigDecimal activityPickUpPrice = activityItemPrice.getActivityPickUpPrice();

                BigDecimal platformDepositUnitPrice = activityItemPrice.getPlatformDepositUnitPrice();
                BigDecimal platformPickUpPrice = activityItemPrice.getPlatformPickUpPrice();

                // 重新计算活动订金总价与活动总价（成本价）
                BigDecimal activityDepositTotalPrice = NumberUtil.mul(activityDepositUnitPrice, quantityTotalBulk);
                // 订金变更值
                BigDecimal depositChangeValue = NumberUtil.mul(activityDepositUnitPrice, differenceValueTotal);
                // 活动订金剩余
                BigDecimal newActivityDepositSurplusPrice = NumberUtil.add(activityDepositSurplusPrice, depositChangeValue);

                BigDecimal activityTotalPrice = NumberUtil.mul(activityPickUpPrice, quantityTotalBulk);
                // 重新计算平台订金总价与平台总价（平台价）
                BigDecimal platformDepositTotalPrice = NumberUtil.mul(platformDepositUnitPrice, quantityTotalBulk);
                BigDecimal platformTotalPrice = NumberUtil.mul(platformPickUpPrice, quantityTotalBulk);

                activityItemPrice.setActivityDepositSurplusPrice(newActivityDepositSurplusPrice);
                activityItemPrice.setActivityDepositTotalPrice(activityDepositTotalPrice);
                activityItemPrice.setActivityTotalPrice(activityTotalPrice);
                activityItemPrice.setPlatformDepositTotalPrice(platformDepositTotalPrice);
                activityItemPrice.setPlatformTotalPrice(platformTotalPrice);

                TenantHelper.ignore(() -> {
                    iProductActivityStockService.updateBatchById(stockList);
                    iProductActivityStockItemService.updateBatchById(stockItemList);
                    iProductActivityPriceItemService.updateById(activityItemPrice);
                });

                // 日志生成DTO
                // ActivityLockDTO activityLockDTO = new ActivityLockDTO();
                if (ProductActivityTypeEnum.StockLock.equals(activityType)) {
                    // 供货商锁货信息主体
                    ProductActivityStockLock activityStockLock =
                        iProductActivityStockLockService.queryByProductActivityId(productActivityId);
                    Integer oldAlready = activityStockLock.getStockLockQuantityAlready();
                    Integer newAlready = oldAlready + differenceValueTotal;
                    activityStockLock.setStockLockQuantityAlready(newAlready).resetSurplus();
                    productActivity.setQuantityAlready(newAlready).resetSurplus();

                    // 分销商锁货信息主体
                    ProductActivityStockLockItem activityStockLockItem =
                        iProductActivityStockLockItemService.queryByActivityItemId(activityItemId);
                    Integer oldStockLockSurplus_item = activityStockLockItem.getStockLockSurplus();
                    Integer oldStockLockQuantity_item = activityStockLockItem.getStockLockQuantity();
                    Integer newStockLockSurplus_item = oldStockLockSurplus_item + differenceValueTotal;
                    Integer newStockLockQuantity_item = oldStockLockQuantity_item + differenceValueTotal;
                    activityStockLockItem.setStockLockQuantity(newStockLockQuantity_item);
                    activityStockLockItem.setStockLockSurplus(newStockLockSurplus_item);

                    activityItem.setQuantityTotal(newStockLockQuantity_item);
                    activityItem.setQuantitySurplus(newStockLockSurplus_item);

                    TenantHelper.ignore(() -> {
                        iProductActivityItemService.updateById(activityItem);
                        iProductActivityStockLockService.updateById(activityStockLock);
                        iProductActivityStockLockItemService.updateById(activityStockLockItem);
                        iProductActivityService.updateById(productActivity);
                    });

                    // activityLockDTO.setStockLockEntity(activityStockLock);
                    // activityLockDTO.setStockLockItemEntity(activityStockLockItem);
                } else if (ProductActivityTypeEnum.Buyout.equals(activityType)) {
                    // 供货商圈货信息主体
                    ProductActivityBuyout activityBuyout =
                        iProductActivityBuyoutService.queryByProductActivityId(productActivityId);
                    Integer oldAlready = activityBuyout.getBuyoutQuantityAlready();
                    Integer newAlready = oldAlready + differenceValueTotal;
                    activityBuyout.setBuyoutQuantityAlready(newAlready).resetSurplus();
                    productActivity.setQuantityAlready(newAlready).resetSurplus();

                    // 分销商圈货信息主体
                    ProductActivityBuyoutItem activityBuyoutItem =
                        iProductActivityBuyoutItemService.queryByActivityItemId(activityItemId);

                    Integer oldBuyoutSurplus_item = activityBuyoutItem.getBuyoutSurplus();
                    Integer oldBuyoutQuantity_item = activityBuyoutItem.getBuyoutQuantity();
                    Integer newBuyoutSurplus_item = oldBuyoutSurplus_item + differenceValueTotal;
                    Integer newBuyoutQuantity_item = oldBuyoutQuantity_item + differenceValueTotal;
                    activityBuyoutItem.setBuyoutQuantity(newBuyoutQuantity_item);
                    activityBuyoutItem.setBuyoutSurplus(newBuyoutSurplus_item);

                    activityItem.setQuantityTotal(newBuyoutQuantity_item);
                    activityItem.setQuantitySurplus(newBuyoutSurplus_item);

                    TenantHelper.ignore(() -> {
                        iProductActivityItemService.updateById(activityItem);
                        iProductActivityBuyoutService.updateById(activityBuyout);
                        iProductActivityBuyoutItemService.updateById(activityBuyoutItem);
                        iProductActivityService.updateById(productActivity);
                    });
                }

                // 直接操作分销商钱包
                tenantWalletService.walletChanges(dTenantId, dTransactionRecord, true);
                iTransactionsProductActivityItemService.saveRelation(dTransactionRecord.getId(), activityItemId);

                // 创建同步任务以同步库存
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                if (productSku != null) {
                    ZSMallProductEventUtils.createTaskStockSync(productSku.getId());
                }
            } catch (RStatusCodeException e) {  // 状态码异常
                throw e;
            } catch (WalletException e) {  // 钱包异常
                throw new RStatusCodeException(e.getStatusCode());
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                log.info("主商品活动{}解锁成功 消耗时间 {}ms", key, timer.intervalMs());
            }
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST_OR_CANCELLED);
        }
        return R.ok();
    }


}
