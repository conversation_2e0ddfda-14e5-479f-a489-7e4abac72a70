package com.zsmall.activity.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.entity.domain.bo.productActivity.*;
import com.zsmall.activity.entity.domain.vo.productActivity.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo;

import javax.servlet.http.HttpServletRequest;

/**
 * 商品活动相关-业务接口层
 **/
public interface ProductActivityService {

    /**
     * 分页查询活动可用商品列表（供应商）
     */
    TableDataInfo<ProductSkuAndStockVo> getProductList(ProductByActivityBo bo, PageQuery pageQuery);

    /**
     * 分页查询商品活动列表（自动分发至各角色不同处理逻辑）
     */
    TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery);

    /**
     * 创建活动
     */
    R<Void> createActivity(ProductActivityBaseBo bo);

    /**
     * 查询商品活动草稿
     */
    R<ProductActivityDraftVo> getActivityDraft(String activityID);

    /**
     * 查询商品活动详情（平台、供货商）
     */
    R<ProductActivityDetailBaseVo> getActivityDetail(String activityID);

    /**
     * 锁货转圈货
     */
    R<Void> stockLockToBuyout(StockLockToBuyoutBo bo);

    /**
     * 更新活动状态
     */
    R<Void> updateState(ActivityUpdateBo bo);

    /**
     * 分页查询临期商品活动列表
     */
    TableDataInfo<ExpiringActivityItemQueryVo> queryExpiringActivityPage(ExpiringActivityQueryBo bo, PageQuery pageQuery);

    /**
     * 查询分销商活动库存信息
     */
    R<ActivityStockDVo> queryActivityStockItem(String activityID);

    /**
     * 分页查询活动资金信息（管理员）
     */
    ProductActivityPriceTableVo<ProductActivityPriceVo> queryActivityPricePage(ProductActivityPriceBo bo, PageQuery pageQuery);

    /**
     * 导出活动资金信息（管理员）
     */
    R<Void> exportActivityPrice(ProductActivityPriceBo bo);

    /**
     * 取消分销商活动（管理员）
     */
    R<Void> cancelActivityByManager(CancelBulkActivityBo bo) throws Exception;

    /**
     * 调整分销商活动（管理员）
     */
    R<Void> adjustActivityByManager(AdjustActivityDBo bo, HttpServletRequest request) throws Exception;

}
