package com.zsmall.activity.biz.factory;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityUpdateBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityBaseBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityQueryBo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDetailBaseVo;
import com.zsmall.activity.entity.domain.vo.productActivity.ProductActivityDraftVo;
import org.springframework.beans.factory.InitializingBean;

/**
 * 商品活动工厂接口
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
public interface ProductActivityFactoryService extends InitializingBean {

    /**
     * 分页查询商品活动列表
     */
    TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery);

    /**
     * 创建活动
     */
    R<Void> createActivity(ProductActivityBaseBo bo);

    /**
     * 查询商品活动草稿
     */
    R<ProductActivityDraftVo> getActivityDraft(String activityID);

    /**
     * 查询商品活动详情
     */
    R<ProductActivityDetailBaseVo> getActivityDetail(String activityID);

    /**
     * 更新活动状态
     */
    R<Void> updateState(ActivityUpdateBo bo);

}
